# 🧪 Flag Submission Testing Guide

## Overview
This guide provides comprehensive testing procedures for the flag submission system in Wolf CTF Challenge.

## 🎯 Test Scenarios

### 1. Valid Flag Submission
**Test**: Submit correct flag for unsolved problem
```
Input: WOLF{buff3r_0v3rfl0w_b4s1cs}
Expected: Success message, score increase, problem marked as solved
```

### 2. Invalid Flag Format
**Test**: Submit flag with wrong format
```
Input: wolf{test}
Expected: "🚫 INVALID FORMAT - Flag must be in format: WOLF{...}"

Input: WOLF[test]
Expected: "🚫 INVALID FORMAT - Flag must be in format: WOLF{...}"

Input: {test}
Expected: "🚫 INVALID FORMAT - Flag must be in format: WOLF{...}"
```

### 3. Incorrect Flag
**Test**: Submit wrong flag with correct format
```
Input: WOLF{wrong_flag}
Expected: "❌ WRONG FLAG - Incorrect flag. Keep trying!"
```

### 4. Already Solved Problem
**Test**: Submit correct flag for already solved problem
```
Input: WOLF{buff3r_0v3rfl0w_b4s1cs} (for already solved problem)
Expected: "⚠️ ALREADY SOLVED - You've already solved this problem!"
```

### 5. Empty Input
**Test**: Submit empty flag
```
Input: ""
Expected: "🚫 INVALID INPUT - Please enter a flag"
```

### 6. Short Flag
**Test**: Submit very short flag
```
Input: WOLF{}
Expected: "🚫 INVALID FLAG - Flag is too short"
```

## 🔧 System Testing

### 1. Network Connectivity Test
```javascript
// Test Firebase connection
const testConnection = async () => {
  try {
    const testDoc = await getDoc(doc(db, 'users', 'test'));
    console.log('✅ Firebase connection successful');
  } catch (error) {
    console.error('❌ Firebase connection failed:', error);
  }
};
```

### 2. Authentication Test
```javascript
// Test user authentication
const testAuth = () => {
  const user = auth.currentUser;
  if (user) {
    console.log('✅ User authenticated:', user.email);
  } else {
    console.error('❌ User not authenticated');
  }
};
```

### 3. Firestore Rules Test
```javascript
// Test Firestore permissions
const testPermissions = async (userId) => {
  try {
    const userRef = doc(db, 'users', userId);
    const userSnap = await getDoc(userRef);
    console.log('✅ Read permission successful');
    
    await updateDoc(userRef, { testField: 'test' });
    console.log('✅ Write permission successful');
  } catch (error) {
    console.error('❌ Permission test failed:', error);
  }
};
```

## 🚀 Manual Testing Steps

### Pre-Test Setup
1. **Clear Browser Data**
   - Clear cache and cookies
   - Disable browser extensions
   - Use incognito/private mode

2. **Login Process**
   - Navigate to platform
   - Login with test credentials
   - Verify user profile loads

### Test Execution

#### Test 1: Valid Submission
1. Navigate to CTF challenges page
2. Select unsolved problem
3. Enter correct flag: `WOLF{buff3r_0v3rfl0w_b4s1cs}`
4. Click "SUBMIT FLAG"
5. **Expected**: Success toast, score increase, problem marked solved

#### Test 2: Invalid Format
1. Select unsolved problem
2. Enter: `wolf{test}`
3. Click "SUBMIT FLAG"
4. **Expected**: Format error message

#### Test 3: Wrong Flag
1. Select unsolved problem
2. Enter: `WOLF{wrong_answer}`
3. Click "SUBMIT FLAG"
4. **Expected**: Wrong flag message

#### Test 4: Already Solved
1. Select already solved problem
2. Enter correct flag
3. Click "SUBMIT FLAG"
4. **Expected**: Already solved message

#### Test 5: Network Error Simulation
1. Disconnect internet
2. Try to submit flag
3. **Expected**: Connection error message
4. Reconnect internet and retry

## 🔍 Error Monitoring

### Browser Console Checks
```javascript
// Check for JavaScript errors
console.error = (function(originalError) {
  return function(...args) {
    // Log errors for testing
    console.log('🚨 Error detected:', ...args);
    originalError.apply(console, args);
  };
})(console.error);
```

### Network Tab Monitoring
1. Open Developer Tools
2. Go to Network tab
3. Submit flag
4. Check for:
   - Failed requests (red status)
   - Slow requests (>5 seconds)
   - 403/401 errors (permission issues)
   - 500 errors (server issues)

## 📊 Performance Testing

### Load Testing
```javascript
// Test multiple rapid submissions (don't actually run this)
const loadTest = async () => {
  const promises = [];
  for (let i = 0; i < 10; i++) {
    promises.push(submitFlag(userId, problemId, flag, correctFlag, points));
  }
  
  try {
    const results = await Promise.all(promises);
    console.log('Load test results:', results);
  } catch (error) {
    console.error('Load test failed:', error);
  }
};
```

### Response Time Testing
```javascript
// Measure submission response time
const measureResponseTime = async () => {
  const startTime = performance.now();
  
  try {
    await submitFlag(userId, problemId, flag, correctFlag, points);
    const endTime = performance.now();
    console.log(`Response time: ${endTime - startTime}ms`);
  } catch (error) {
    console.error('Response time test failed:', error);
  }
};
```

## 🛡️ Security Testing

### 1. SQL Injection Test
```
Input: WOLF{'; DROP TABLE users; --}
Expected: Treated as normal flag, no database impact
```

### 2. XSS Test
```
Input: WOLF{<script>alert('xss')</script>}
Expected: Sanitized input, no script execution
```

### 3. Long Input Test
```
Input: WOLF{very_long_flag_with_thousands_of_characters...}
Expected: Handled gracefully, appropriate error message
```

## 📋 Test Results Template

### Test Report
```
Date: [DATE]
Tester: [NAME]
Browser: [BROWSER VERSION]
Platform: [OS]

Test Results:
✅ Valid flag submission: PASS/FAIL
✅ Invalid format handling: PASS/FAIL
✅ Wrong flag handling: PASS/FAIL
✅ Already solved handling: PASS/FAIL
✅ Empty input handling: PASS/FAIL
✅ Network error handling: PASS/FAIL
✅ Authentication check: PASS/FAIL
✅ Permission verification: PASS/FAIL
✅ Performance (< 2s): PASS/FAIL
✅ Security measures: PASS/FAIL

Issues Found:
- [List any issues]

Recommendations:
- [List recommendations]
```

## 🔧 Automated Testing

### Jest Test Example
```javascript
describe('Flag Submission', () => {
  test('should accept valid flag', async () => {
    const result = await submitFlag(
      'testUser', 
      'prob1', 
      'WOLF{buff3r_0v3rfl0w_b4s1cs}',
      'WOLF{buff3r_0v3rfl0w_b4s1cs}',
      50
    );
    expect(result.success).toBe(true);
  });

  test('should reject invalid format', async () => {
    const result = await submitFlag(
      'testUser',
      'prob1',
      'wolf{test}',
      'WOLF{correct}',
      50
    );
    expect(result.success).toBe(false);
    expect(result.reason).toBe('invalid_format');
  });
});
```

## 🚨 Emergency Procedures

### If Flag Submission Completely Fails
1. **Immediate Actions**
   - Switch to manual verification
   - Notify participants of issue
   - Document all attempts

2. **Fallback System**
   - Use simple submission method
   - Disable enhanced features temporarily
   - Monitor for resolution

3. **Recovery Steps**
   - Identify root cause
   - Apply fix
   - Test thoroughly
   - Re-enable full system

---

🧪 **Use this guide to thoroughly test the flag submission system and ensure reliable operation!**

Regular testing helps maintain system integrity and user experience.
