import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { LiveLeaderboard } from '@/components/LiveLeaderboard';
import { getOrCreateUserProfile, updateUserScore, submitFlag, submitFlagSimple, ctfProblems, logOut } from '@/lib/firebase';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/components/AuthProvider';
import { updateDoc, doc, serverTimestamp } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { useNavigate } from 'react-router-dom';

interface UserProfile {
  score: number;
  solvedProblems: string[];
  displayName: string;
  fullName: string;
  email: string;
}

const CTF = () => {
  const { currentUser: user, loading } = useAuth();
  const navigate = useNavigate();
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [flagInputs, setFlagInputs] = useState<Record<string, string>>({});
  const [submitting, setSubmitting] = useState<Record<string, boolean>>({});
  const [showLeaderboard, setShowLeaderboard] = useState(false);
  const { toast } = useToast();

  // Admin check
  const adminEmails = ['<EMAIL>', 'tamilselvanadmin'];
  const isAdmin = user && adminEmails.includes(user.email || '');

  // Helper functions for error handling
  const getErrorTitle = (reason?: string) => {
    switch (reason) {
      case 'already_solved': return '⚠️ ALREADY SOLVED';
      case 'invalid_format': return '🚫 INVALID FORMAT';
      case 'invalid_input': return '🚫 INVALID INPUT';
      case 'invalid_length': return '🚫 INVALID FLAG';
      case 'incorrect_flag': return '❌ WRONG FLAG';
      case 'permission_denied': return '🔐 AUTHENTICATION ERROR';
      case 'unauthenticated': return '🔐 SESSION EXPIRED';
      case 'not_authenticated': return '🔐 LOGIN REQUIRED';
      case 'profile_missing': return '👤 PROFILE ERROR';
      case 'profile_error': return '👤 PROFILE ERROR';
      case 'unavailable': return '🌐 SERVICE UNAVAILABLE';
      case 'user_not_found': return '👤 USER ERROR';
      case 'system_error': return '🚨 SYSTEM ERROR';
      default: return '🚫 SUBMISSION FAILED';
    }
  };

  const getErrorMessage = (reason?: string) => {
    switch (reason) {
      case 'already_solved': return 'You have already solved this problem!';
      case 'invalid_format': return 'Flag must be in format: WOLF{...}';
      case 'invalid_input': return 'Please enter a flag';
      case 'invalid_length': return 'Flag is too short';
      case 'incorrect_flag': return 'Incorrect flag. Keep trying!';
      case 'permission_denied': return 'Authentication expired. Please logout and login again.';
      case 'unauthenticated': return 'Session expired. Please login again.';
      case 'not_authenticated': return 'Please login to submit flags.';
      case 'profile_missing': return 'User profile not found. Please refresh the page and try again.';
      case 'profile_error': return 'Profile error. Please refresh the page and try again.';
      case 'unavailable': return 'Service temporarily unavailable. Please try again in a moment.';
      case 'user_not_found': return 'User profile not found. Please logout and login again.';
      case 'system_error': return 'System error. Please try again.';
      default: return 'Please try again';
    }
  };

  // Enhanced profile loading with automatic creation and retry logic
  const refreshUserProfile = async (retryAttempt = 0) => {
    if (user && user.email) {
      try {
        console.log(`Loading user profile... (attempt ${retryAttempt + 1})`);

        // Use getOrCreateUserProfile to ensure profile always exists
        const profile = await getOrCreateUserProfile(user.uid, user.email, user.displayName || undefined);
        if (profile) {
          setUserProfile(profile as UserProfile);
          console.log('User profile loaded successfully');
        } else {
          throw new Error('Profile creation failed');
        }
      } catch (error: any) {
        console.error('Failed to refresh user profile:', error);

        // Retry logic for profile loading
        if (retryAttempt < 2) {
          console.log(`Retrying profile load in ${(retryAttempt + 1) * 2} seconds...`);
          setTimeout(() => {
            refreshUserProfile(retryAttempt + 1);
          }, (retryAttempt + 1) * 2000);

          if (retryAttempt === 0) {
            toast({
              title: "⏳ LOADING PROFILE",
              description: "Profile loading taking longer than expected. Retrying...",
            });
          }
        } else {
          // Final failure after retries
          let errorMessage = "Failed to load profile. Please refresh the page.";

          if (error.code === 'permission-denied') {
            errorMessage = "Permission denied. Please logout and login again.";
          } else if (error.code === 'unavailable') {
            errorMessage = "Service temporarily unavailable. Please try again in a moment.";
          } else if (error.message?.includes('network')) {
            errorMessage = "Network connection error. Check your internet connection.";
          }

          toast({
            variant: "destructive",
            title: "🚫 PROFILE ERROR",
            description: errorMessage + " Click to retry.",
          });

          // Add retry button functionality
          setTimeout(() => {
            if (!userProfile) {
              console.log("Auto-retrying profile load after error...");
              refreshUserProfile(0);
            }
          }, 5000);
        }
      }
    }
  };

  useEffect(() => {
    if (user) {
      refreshUserProfile();
    }
  }, [user]);

  const handleFlagSubmit = async (problemId: string, correctFlag: string, points: number) => {
    // Check authentication first
    if (!user) {
      toast({
        variant: "destructive",
        title: "🚫 NOT AUTHENTICATED",
        description: "Please login to submit flags",
      });
      return;
    }

    // Check if user profile is loaded
    if (!userProfile) {
      toast({
        variant: "destructive",
        title: "⏳ LOADING PROFILE",
        description: "Please wait for your profile to load",
      });
      // Try to refresh profile
      refreshUserProfile();
      return;
    }

    const submittedFlag = flagInputs[problemId]?.trim();

    // Enhanced input validation
    if (!submittedFlag) {
      toast({
        variant: "destructive",
        title: "🚫 INVALID INPUT",
        description: "Please enter a flag",
      });
      return;
    }

    // Check flag format
    if (!submittedFlag.startsWith('WOLF{') || !submittedFlag.endsWith('}')) {
      toast({
        variant: "destructive",
        title: "🚫 INVALID FORMAT",
        description: "Flag must be in format: WOLF{...}",
      });
      return;
    }

    // Check minimum flag length
    if (submittedFlag.length < 8) {
      toast({
        variant: "destructive",
        title: "🚫 INVALID FLAG",
        description: "Flag is too short",
      });
      return;
    }

    // Check if already solved locally first
    if (userProfile.solvedProblems.includes(problemId)) {
      toast({
        variant: "destructive",
        title: "⚠️ ALREADY SOLVED",
        description: "You have already solved this problem!",
      });
      return;
    }

    setSubmitting(prev => ({ ...prev, [problemId]: true }));

    try {
      // Try enhanced submitFlag function first
      let result: { success: boolean; reason?: string; newScore?: number; message?: string; totalSolved?: number };

      try {
        result = await submitFlag(user!.uid, problemId, submittedFlag, correctFlag, points);
      } catch (enhancedError) {
        console.warn('Enhanced submission failed, trying simple method:', enhancedError);
        // Fallback to simple submission method
        try {
          result = await submitFlagSimple(user!.uid, problemId, submittedFlag, correctFlag, points);
        } catch (fallbackError) {
          console.error('Both submission methods failed:', fallbackError);
          result = {
            success: false,
            reason: 'system_error',
            message: 'System error. Please try again later.'
          };
        }
      }

      if (result.success) {
        // Success handling
        toast({
          title: "🎉 FLAG CAPTURED!",
          description: result.message || `Earned ${points} points! Great work, hacker!`,
        });

        // Update local state with new score
        setUserProfile(prev => {
          if (!prev) return null;

          const newSolvedProblems = prev.solvedProblems.includes(problemId)
            ? prev.solvedProblems
            : [...prev.solvedProblems, problemId];

          return {
            ...prev,
            score: result.newScore || (prev.score + points),
            solvedProblems: newSolvedProblems
          };
        });

        // Clear input
        setFlagInputs(prev => ({ ...prev, [problemId]: '' }));

        // Refresh user profile to ensure synchronization
        setTimeout(() => {
          refreshUserProfile();
        }, 500);

        // Show achievement for perfect score
        const finalScore = result.newScore || (userProfile?.score || 0) + points;
        if (finalScore >= 250) {
          setTimeout(() => {
            toast({
              title: "🏆 ELITE HACKER ACHIEVED!",
              description: "Perfect score! You've mastered all challenges!",
            });
          }, 2000);
        }

        // Show progress update
        if (result.totalSolved) {
          setTimeout(() => {
            toast({
              title: "📊 PROGRESS UPDATE",
              description: `${result.totalSolved}/5 problems solved! Score: ${finalScore}`,
            });
          }, 1000);
        }

      } else {
        // Error handling with specific messages
        const errorTitle = getErrorTitle(result.reason);
        const errorMessage = result.message || getErrorMessage(result.reason);

        // Handle authentication errors specially
        if (result.reason === 'permission_denied' || result.reason === 'unauthenticated' || result.reason === 'not_authenticated') {
          toast({
            variant: "destructive",
            title: errorTitle,
            description: errorMessage + " Try refreshing the page.",
          });

          // Auto-refresh profile after a delay
          setTimeout(() => {
            refreshUserProfile();
          }, 2000);

        } else if (result.reason === 'profile_missing' || result.reason === 'profile_error') {
          toast({
            variant: "destructive",
            title: errorTitle,
            description: errorMessage,
          });

          // Auto-refresh profile
          setTimeout(() => {
            refreshUserProfile();
          }, 1000);

        } else {
          toast({
            variant: "destructive",
            title: errorTitle,
            description: errorMessage,
          });
        }
      }
    } catch (error: any) {
      console.error('Flag submission error:', error);

      // Handle specific Firebase errors
      if (error.code === 'permission-denied') {
        toast({
          variant: "destructive",
          title: "🚫 PERMISSION DENIED",
          description: "You don't have permission to submit flags. Please login again.",
        });
      } else if (error.code === 'unavailable') {
        toast({
          variant: "destructive",
          title: "🌐 CONNECTION ERROR",
          description: "Unable to connect to server. Check your internet connection.",
        });
      } else if (error.message === 'User not found') {
        toast({
          variant: "destructive",
          title: "👤 USER ERROR",
          description: "User profile not found. Please logout and login again.",
        });
      } else {
        toast({
          variant: "destructive",
          title: "🚨 SYSTEM ERROR",
          description: `Failed to submit flag: ${error.message || 'Unknown error'}`,
        });
      }
    }

    setSubmitting(prev => ({ ...prev, [problemId]: false }));
  };

  const handleLogout = async () => {
    try {
      await logOut();
      toast({
        title: "LOGGED OUT",
        description: "Session terminated successfully",
      });
      // Navigate to home page after logout
      navigate('/');
    } catch (error) {
      console.error('Logout error:', error);
      toast({
        variant: "destructive",
        title: "LOGOUT ERROR",
        description: "Failed to logout properly",
      });
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="terminal-cursor text-4xl mb-4">LOADING</div>
          <p className="text-muted-foreground font-mono">Initializing CTF system...</p>
        </div>
      </div>
    );
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'EASY': return 'bg-terminal-green text-black';
      case 'MEDIUM': return 'bg-warning text-black';
      case 'HARD': return 'bg-destructive text-white';
      default: return 'bg-muted';
    }
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Enhanced Security overlay */}
      <div
        className="fixed inset-0 z-50 pointer-events-none"
        onContextMenu={(e) => {
          e.preventDefault();
          toast({
            variant: "destructive",
            title: "🚫 ACCESS DENIED",
            description: "Right-click is disabled for security",
          });
          return false;
        }}
        onDragStart={(e) => {
          e.preventDefault();
          return false;
        }}
        style={{
          pointerEvents: 'none',
          userSelect: 'none',
          WebkitUserSelect: 'none',
          MozUserSelect: 'none',
          msUserSelect: 'none'
        }}
      />
      
      {/* Header */}
      <div className="border-b-4 border-foreground bg-background sticky top-0 z-40">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold tracking-tighter">WOLF CTF CHALLENGE</h1>
            <p className="text-sm text-muted-foreground font-mono">
              HACKER: {userProfile?.displayName || userProfile?.fullName} | SCORE: {userProfile?.score || 0}/250
            </p>
          </div>
          <div className="flex flex-wrap gap-2">
            <Button
              variant={showLeaderboard ? "default" : "outline"}
              onClick={() => setShowLeaderboard(!showLeaderboard)}
              className="border-2"
            >
              🏆 LEADERBOARD
            </Button>
            {isAdmin && (
              <Button variant="accent" onClick={() => navigate('/admin')}>
                🛡️ ADMIN
              </Button>
            )}
            <Button variant="outline" onClick={() => navigate('/profile')}>
              👤 PROFILE
            </Button>
            <Button variant="destructive" onClick={handleLogout}>
              LOGOUT
            </Button>
          </div>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="bg-muted border-b-2 border-foreground">
        <div className="container mx-auto px-4 py-2">
          <div className="flex items-center gap-4">
            <span className="text-sm font-bold">PROGRESS:</span>
            <div className="flex-1 h-4 bg-background border-2 border-foreground relative">
              <div 
                className="h-full bg-accent transition-all duration-500"
                style={{ width: `${((userProfile?.score || 0) / 250) * 100}%` }}
              />
            </div>
            <span className="text-sm font-bold">
              {userProfile?.solvedProblems?.length || 0}/5 SOLVED
            </span>
          </div>
        </div>
      </div>

      {/* Leaderboard Section (Toggleable) */}
      {showLeaderboard && (
        <div className="bg-muted border-b-2 border-foreground">
          <div className="container mx-auto px-4 py-6">
            <Card className="p-4 sm:p-6 border-2 border-foreground">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 gap-2">
                <h2 className="text-xl sm:text-2xl font-bold">🏆 LIVE LEADERBOARD</h2>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowLeaderboard(false)}
                  className="border-2 self-start sm:self-auto"
                >
                  ✕ CLOSE
                </Button>
              </div>
              <LiveLeaderboard />
            </Card>
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className="container mx-auto px-4 py-6 sm:py-8">
        <div className="grid gap-4 sm:gap-6 lg:grid-cols-3">
          {/* Problems Section */}
          <div className="lg:col-span-2">
            <h2 className="text-lg sm:text-xl font-bold mb-4 sm:mb-6">🎯 CHALLENGES</h2>
            <div className="grid gap-4 sm:gap-6 md:grid-cols-2">
              {ctfProblems.map((problem) => {
                const isSolved = userProfile?.solvedProblems?.includes(problem.id);
                const isSubmitting = submitting[problem.id];
                
                return (
                  <Card
                    key={problem.id}
                    className={`p-4 sm:p-6 border-2 sm:border-4 shadow-brutal-lg transition-all ${
                      isSolved ? 'border-terminal-green bg-terminal-green/10' : 'border-foreground'
                    }`}
                  >
                    <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start mb-3 sm:mb-4 gap-2">
                      <h3 className="text-base sm:text-lg font-bold">{problem.title}</h3>
                      <div className="flex gap-2 self-start sm:self-auto">
                        <Badge className={`${getDifficultyColor(problem.difficulty)} text-xs sm:text-sm`}>
                          {problem.difficulty}
                        </Badge>
                        <Badge variant="outline" className="border-foreground text-xs sm:text-sm">
                          {problem.points}pts
                        </Badge>
                      </div>
                    </div>
                    
                    {/* Clean description */}
                    <p className="text-xs sm:text-sm text-muted-foreground mb-3 font-mono leading-relaxed">
                      {problem.description}
                    </p>

                    {/* Challenge Link Button */}
                    {(problem as any).challengeUrl && (
                      <div className="mb-4">
                        <Button
                          onClick={() => {
                            window.open((problem as any).challengeUrl, '_blank', 'noopener,noreferrer');
                          }}
                          variant="outline"
                          className="w-full border-2 border-accent bg-accent/10 hover:bg-accent/20 text-accent-foreground font-bold transition-all duration-200 hover:scale-105"
                        >
                          🔗 OPEN CHALLENGE
                        </Button>
                        <p className="text-xs text-muted-foreground mt-2 text-center font-mono">
                          Click to open the challenge in a new tab
                        </p>
                      </div>
                    )}

                    {problem.hint && (
                      <p className="text-xs text-warning mb-3 sm:mb-4 font-mono p-2 bg-warning/10 border border-warning/20 rounded">
                        💡 HINT: {problem.hint}
                      </p>
                    )}

                    {isSolved ? (
                      <div className="text-center py-3 sm:py-4">
                        <div className="text-xl sm:text-2xl mb-2">🎯</div>
                        <p className="text-terminal-green font-bold text-sm sm:text-base">SOLVED!</p>
                        <p className="text-xs sm:text-sm text-muted-foreground mt-1">
                          Flag: <code className="bg-muted px-1 py-0.5 rounded text-xs break-all">{problem.flag}</code>
                        </p>
                      </div>
                    ) : (
                      <div className="space-y-2 sm:space-y-3">
                        <Input
                          placeholder="WOLF{enter_flag_here}"
                          value={flagInputs[problem.id] || ''}
                          onChange={(e) => setFlagInputs(prev => ({
                            ...prev,
                            [problem.id]: e.target.value
                          }))}
                          disabled={isSubmitting}
                          className="font-mono text-sm sm:text-base"
                        />
                        <Button
                          onClick={() => handleFlagSubmit(problem.id, problem.flag, problem.points)}
                          disabled={isSubmitting}
                          variant="accent"
                          className="w-full text-sm sm:text-base py-2 sm:py-3"
                        >
                          {isSubmitting ? 'VERIFYING...' : 'SUBMIT FLAG'}
                        </Button>
                      </div>
                    )}
                  </Card>
                );
              })}
            </div>
          </div>

          {/* Live Leaderboard Sidebar */}
          <div className="lg:col-span-1">
            <LiveLeaderboard />
          </div>
        </div>

        {/* Achievement Section */}
        {userProfile?.score === 250 && (
          <div className="mt-12">
            <Card className="p-8 border-4 border-accent shadow-brutal-lg bg-accent/10">
              <div className="text-center">
                <div className="text-6xl mb-4">🏆</div>
                <h2 className="text-3xl font-bold mb-2">CONGRATULATIONS!</h2>
                <p className="text-lg text-muted-foreground">
                  You've conquered all challenges and achieved the rank of 
                  <span className="text-accent font-bold"> ELITE HACKER</span>!
                </p>
                <p className="text-sm mt-4 font-mono">
                  Perfect Score: 250/250 Points | All 5 Problems Solved
                </p>
              </div>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
};

export default CTF;