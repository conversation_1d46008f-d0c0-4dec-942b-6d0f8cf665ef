import { useNavigate } from 'react-router-dom';
import { useToast } from '@/hooks/use-toast';
import { logOut } from '@/lib/firebase';

export const useLogout = () => {
  const navigate = useNavigate();
  const { toast } = useToast();

  const handleLogout = async () => {
    try {
      await logOut();
      
      toast({
        title: "LOGGED OUT",
        description: "Session terminated successfully",
      });
      
      // Navigate to home page after successful logout
      navigate('/', { replace: true });
      
    } catch (error) {
      console.error('Logout error:', error);
      
      toast({
        variant: "destructive",
        title: "LOGOUT ERROR",
        description: "Failed to logout properly. Please try again.",
      });
      
      // Even if logout fails, try to navigate to home
      navigate('/', { replace: true });
    }
  };

  return { handleLogout };
};
