# 🔒 Firebase Security Rules Setup

This document explains how to deploy the Firebase security rules for your Wolf CTF Community application.

## Files Created

1. **`firestore.rules`** - Security rules for Firestore database
2. **`storage.rules`** - Security rules for Firebase Storage

## Deploying the Rules

### Option 1: Using Firebase Console (Recommended for beginners)

#### Firestore Rules:
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your project: `cyber-wolf-community-ctf`
3. Navigate to **Firestore Database** → **Rules**
4. Copy the content from `firestore.rules` and paste it into the rules editor
5. Click **Publish**

#### Storage Rules:
1. In the same Firebase Console
2. Navigate to **Storage** → **Rules**
3. Copy the content from `storage.rules` and paste it into the rules editor
4. Click **Publish**

### Option 2: Using Firebase CLI (Advanced)

1. **Install Firebase CLI** (if not already installed):
   ```bash
   npm install -g firebase-tools
   ```

2. **Login to Firebase**:
   ```bash
   firebase login
   ```

3. **Initialize Firebase in your project** (if not already done):
   ```bash
   firebase init
   ```
   - Select Firestore and Storage
   - Choose your existing project: `cyber-wolf-community-ctf`
   - Accept default file names or specify custom ones

4. **Deploy the rules**:
   ```bash
   firebase deploy --only firestore:rules,storage
   ```

## Security Features Implemented

### Firestore Rules:
- ✅ **User Isolation**: Users can only access their own profile data
- ✅ **Data Validation**: Enforces proper data structure for user profiles
- ✅ **Read-only CTF Problems**: Prevents tampering with challenge data
- ✅ **Submission Tracking**: Users can create but not modify submissions
- ✅ **Admin Access**: Special permissions for admin users
- ✅ **Score Integrity**: Prevents direct score manipulation

### Storage Rules:
- ✅ **Profile Images**: Users can upload their own profile pictures (5MB limit)
- ✅ **File Type Validation**: Only allows specific file types
- ✅ **Size Limits**: Prevents large file uploads
- ✅ **User Isolation**: Users can only access their own files
- ✅ **Admin Controls**: Special access for administrative files

## Customization

### Adding Admin Users:
Edit both rule files and update the `isAdmin()` function:

```javascript
function isAdmin() {
  return request.auth.token.email in [
    '<EMAIL>',
    '<EMAIL>'
  ];
}
```

### Adjusting File Size Limits:
In `storage.rules`, modify the size checks:
```javascript
resource.size < 10 * 1024 * 1024; // 10MB limit
```

### Adding New Collections:
For new Firestore collections, add rules in `firestore.rules`:
```javascript
match /new_collection/{docId} {
  allow read, write: if request.auth != null;
}
```

## Testing Rules

### Firestore Rules Testing:
1. Go to Firebase Console → Firestore → Rules
2. Click on **Rules playground**
3. Test different scenarios with authenticated/unauthenticated users

### Storage Rules Testing:
1. Go to Firebase Console → Storage → Rules
2. Use the **Rules playground** to test file access scenarios

## Security Best Practices

1. **Never allow unrestricted access** (`allow read, write: if true`)
2. **Always validate user authentication** (`request.auth != null`)
3. **Implement data validation** for critical fields
4. **Use least privilege principle** - only grant necessary permissions
5. **Regularly review and update rules** as your app evolves
6. **Test rules thoroughly** before deploying to production

## Troubleshooting

### Common Issues:
- **Permission denied errors**: Check if user is authenticated and has proper permissions
- **Data validation failures**: Ensure your app sends data in the expected format
- **File upload failures**: Check file type and size restrictions

### Debug Mode:
Enable debug mode in your app to see detailed rule evaluation:
```javascript
// In your Firebase config
const db = getFirestore(app);
if (process.env.NODE_ENV === 'development') {
  connectFirestoreEmulator(db, 'localhost', 8080);
}
```

## Production Considerations

Before going to production:
1. Review all admin email addresses
2. Test all user flows with the rules enabled
3. Consider implementing rate limiting
4. Set up monitoring for rule violations
5. Create backup procedures for rule changes

---

🔥 **Your CTF platform is now secured with proper Firebase rules!**
