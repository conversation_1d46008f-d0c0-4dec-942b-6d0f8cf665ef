# 📧 Firebase Email Account & Profile Management Setup

## Overview
Complete guide for setting up a dedicated Firebase email account, proper user profile creation, and eliminating profile loading errors.

## 🔧 Firebase Email Account Setup

### 1. Create Dedicated Firebase Email Account
```
Recommended Email: <EMAIL>
Alternative: <EMAIL>

Steps:
1. Go to gmail.com
2. Create new account with dedicated email
3. Use strong password and enable 2FA
4. This email will be used for Firebase project management
```

### 2. Firebase Project Configuration
```
1. Go to console.firebase.google.com
2. Login with your dedicated Firebase email
3. Select your project: cyber-wolf-community-ctf
4. Go to Project Settings
5. Verify project configuration:
   - Project ID: cyber-wolf-community-ctf
   - Project Name: Wolf CTF Challenge
   - Default GCP resource location: us-central1
```

### 3. Authentication Setup
```
1. Go to Authentication → Sign-in method
2. Enable Email/Password authentication
3. Configure settings:
   ✅ Email/Password: Enabled
   ✅ Email verification: Optional (recommended for production)
   ✅ Password reset: Enabled
   
4. Add authorized domains if needed:
   - localhost (for development)
   - your-domain.com (for production)
```

## 🗄️ Firestore Database Setup

### 1. Database Configuration
```
1. Go to Firestore Database
2. Create database if not exists
3. Start in test mode (we'll secure with rules)
4. Choose location: us-central1 (or your preferred region)
```

### 2. Collections Structure
```
📁 users/
  📄 {userId}/
    - uid: string
    - email: string
    - displayName: string
    - fullName: string
    - score: number (0-1000)
    - solvedProblems: array
    - createdAt: timestamp
    - updatedAt: timestamp
    - lastLogin: timestamp
    - profileComplete: boolean
    - isActive: boolean

📁 submissions/
  📄 {submissionId}/
    - userId: string
    - problemId: string
    - submittedFlag: string
    - isCorrect: boolean
    - timestamp: timestamp
    - userAgent: string
```

## 🔐 Enhanced Firebase Rules

### Firestore Rules (firestore.rules)
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users collection - enhanced profile management
    match /users/{userId} {
      // Allow all authenticated users to read (for leaderboard)
      allow read: if request.auth != null;
      
      // Allow users to create their own profile
      allow create: if request.auth != null 
        && request.auth.uid == userId
        && validateUserData(request.resource.data)
        && request.resource.data.uid == userId
        && request.resource.data.email == request.auth.token.email;
      
      // Allow users to update their own profile
      allow update: if request.auth != null 
        && request.auth.uid == userId
        && validateUserDataUpdate(request.resource.data, resource.data);
      
      // Allow admins full access
      allow read, write: if request.auth != null && isAdmin();
    }
    
    // Submissions collection
    match /submissions/{submissionId} {
      allow read: if request.auth != null && request.auth.uid == resource.data.userId;
      allow create: if request.auth != null 
        && request.auth.uid == request.resource.data.userId
        && validateSubmissionData(request.resource.data);
    }
  }
  
  // Validation functions
  function validateUserData(data) {
    return data.keys().hasAll(['uid', 'email', 'displayName', 'fullName', 'score', 'solvedProblems', 'createdAt', 'profileComplete'])
      && data.uid is string
      && data.email is string
      && data.score is number
      && data.score >= 0
      && data.score <= 1000
      && data.solvedProblems is list
      && data.profileComplete is bool;
  }
  
  function validateUserDataUpdate(newData, oldData) {
    return newData.uid == oldData.uid
      && newData.email == oldData.email
      && newData.createdAt == oldData.createdAt
      && newData.score >= oldData.score
      && validateUserData(newData);
  }
  
  function isAdmin() {
    return request.auth.token.email in [
      '<EMAIL>',
      'tamilselvanadmin'
    ];
  }
}
```

## 🚀 Enhanced Profile Management System

### Automatic Profile Creation
```typescript
// Enhanced profile creation function
export const getOrCreateUserProfile = async (userId: string, userEmail: string, displayName?: string) => {
  try {
    // Try to get existing profile
    let profile = await getUserProfile(userId);
    
    if (!profile) {
      // Create new profile automatically
      console.log('Creating new user profile for:', userEmail);
      profile = await createUserProfile(userId, {
        email: userEmail,
        displayName: displayName || userEmail.split('@')[0],
        fullName: displayName || userEmail.split('@')[0]
      });
    } else {
      // Update last login time
      const userRef = doc(db, 'users', userId);
      await updateDoc(userRef, {
        lastLogin: serverTimestamp(),
        updatedAt: serverTimestamp()
      });
    }
    
    return profile;
  } catch (error) {
    console.error('Error in getOrCreateUserProfile:', error);
    throw error;
  }
};
```

### Profile Data Structure
```typescript
interface UserProfile {
  uid: string;
  email: string;
  displayName: string;
  fullName: string;
  score: number;
  solvedProblems: string[];
  createdAt: Date;
  updatedAt: Date;
  lastLogin: Date;
  profileComplete: boolean;
  isActive: boolean;
}
```

## 🔧 Deployment Steps

### 1. Deploy Firebase Rules
```bash
# Deploy Firestore rules
firebase deploy --only firestore:rules

# Verify deployment
firebase firestore:rules get
```

### 2. Test Profile Creation
```bash
# Test with new user registration
1. Register new account
2. Check Firestore console
3. Verify profile created automatically
4. Test flag submission
5. Verify score updates
```

### 3. Verify Security
```bash
# Test security rules
1. Try to access other user's data → Should fail
2. Try to create profile with wrong email → Should fail
3. Try to decrease score → Should fail
4. Test admin access → Should work
```

## 🧪 Testing Checklist

### Profile Creation Tests
- [ ] **New User Registration**: Profile created automatically
- [ ] **Email Validation**: Email matches authenticated user
- [ ] **Default Values**: Score starts at 0, empty solved problems
- [ ] **Timestamp Fields**: Created/updated timestamps set correctly
- [ ] **Profile Loading**: Profile loads without errors

### Profile Update Tests
- [ ] **Score Updates**: Score increases correctly
- [ ] **Solved Problems**: Problems added to solved list
- [ ] **Last Login**: Login time updated on each visit
- [ ] **Data Integrity**: Critical fields cannot be changed

### Security Tests
- [ ] **User Isolation**: Users can only access own profile
- [ ] **Admin Access**: Admins can access all profiles
- [ ] **Data Validation**: Invalid data rejected
- [ ] **Score Protection**: Score cannot decrease

## 🚨 Troubleshooting

### Profile Loading Errors
```
Issue: "Profile loading error" messages
Solution:
1. Check Firebase authentication status
2. Verify Firestore rules are deployed
3. Check network connectivity
4. Clear browser cache
5. Test with different browser
```

### Profile Creation Failures
```
Issue: New profiles not created
Solution:
1. Check Firestore rules allow profile creation
2. Verify email authentication is working
3. Check browser console for errors
4. Test Firebase connection
5. Verify project permissions
```

### Permission Denied Errors
```
Issue: "Permission denied" when accessing profile
Solution:
1. Verify user is authenticated
2. Check Firestore rules syntax
3. Deploy latest rules
4. Test with Firebase rules simulator
5. Check user email matches rules
```

## 📊 Monitoring & Maintenance

### Regular Checks
- **User Profiles**: Monitor profile creation success rate
- **Error Rates**: Track profile loading errors
- **Performance**: Monitor profile loading times
- **Security**: Review access patterns and rule violations

### Database Maintenance
- **Cleanup**: Remove inactive user profiles (optional)
- **Backup**: Regular database backups
- **Indexing**: Create indexes for common queries
- **Monitoring**: Set up alerts for errors

---

📧 **Your Firebase email account and profile management system is now professionally configured!**

Users will experience seamless profile creation and management without loading errors.
