# 🚩 Flag Submission Troubleshooting Guide

## Overview
This guide helps resolve common issues with flag submission in the Wolf CTF Challenge platform.

## 🔧 Common Issues & Solutions

### 1. System Error on Flag Submission

#### **Error**: "🚨 SYSTEM ERROR - Failed to submit flag"

**Possible Causes:**
- Firebase connection issues
- Firestore rules problems
- Missing user permissions
- Network connectivity problems

**Solutions:**
1. **Check Internet Connection**
   ```bash
   # Test connectivity
   ping google.com
   ```

2. **Verify Firebase Rules**
   - Ensure Firestore rules allow user submissions
   - Check if user is properly authenticated

3. **Clear Browser Cache**
   - Clear browser cache and cookies
   - Refresh the page
   - Try in incognito/private mode

4. **Logout and Login Again**
   - Click logout button
   - Clear browser storage
   - Login with credentials again

### 2. Permission Denied Errors

#### **Error**: "🚫 PERMISSION DENIED - You don't have permission to submit flags"

**Solutions:**
1. **Re-authenticate**
   ```typescript
   // User needs to logout and login again
   await logOut();
   // Navigate to login page
   ```

2. **Check Firebase Rules**
   - Verify user authentication status
   - Ensure Firestore rules allow submissions

3. **Verify User Profile**
   - Check if user profile exists in Firestore
   - Ensure user has proper permissions

### 3. Connection Errors

#### **Error**: "🌐 CONNECTION ERROR - Unable to connect to server"

**Solutions:**
1. **Network Troubleshooting**
   - Check internet connection
   - Try different network
   - Disable VPN if active

2. **Firebase Status**
   - Check Firebase status page
   - Verify project configuration

3. **Browser Issues**
   - Try different browser
   - Disable browser extensions
   - Clear DNS cache

### 4. Invalid Flag Format

#### **Error**: "🚫 INVALID FORMAT - Flag must be in format: WOLF{...}"

**Solutions:**
1. **Correct Format**
   ```
   ✅ Correct: WOLF{example_flag}
   ❌ Wrong: wolf{example_flag}
   ❌ Wrong: WOLF[example_flag]
   ❌ Wrong: {example_flag}
   ```

2. **Check for Typos**
   - Ensure proper capitalization
   - Check for extra spaces
   - Verify curly braces { }

### 5. Already Solved Problems

#### **Error**: "⚠️ ALREADY SOLVED - You've already solved this problem!"

**Solutions:**
1. **Check Progress**
   - Review solved problems list
   - Verify score has been updated

2. **Refresh Page**
   - Reload the page to sync state
   - Check leaderboard for updates

## 🛠️ Advanced Troubleshooting

### Debug Mode Activation

1. **Open Browser Console** (for debugging only)
   ```javascript
   // Check user authentication
   console.log('User:', firebase.auth().currentUser);
   
   // Check Firestore connection
   console.log('Firestore:', firebase.firestore());
   ```

2. **Check Network Tab**
   - Open Developer Tools
   - Go to Network tab
   - Submit flag and check for failed requests

### Firebase Configuration Check

1. **Verify Config**
   ```typescript
   // Check if Firebase is properly initialized
   const firebaseConfig = {
     apiKey: "AIzaSyDN0E7ombncbYj-_iLhcEYxUGHb1FWo-6E",
     authDomain: "cyber-wolf-community-ctf.firebaseapp.com",
     projectId: "cyber-wolf-community-ctf",
     // ... other config
   };
   ```

2. **Test Connection**
   ```typescript
   // Test Firestore connection
   import { db } from '@/lib/firebase';
   import { doc, getDoc } from 'firebase/firestore';
   
   const testConnection = async () => {
     try {
       const testDoc = await getDoc(doc(db, 'test', 'connection'));
       console.log('Connection successful');
     } catch (error) {
       console.error('Connection failed:', error);
     }
   };
   ```

## 🔍 Error Code Reference

### Firebase Error Codes
- **`permission-denied`**: User lacks required permissions
- **`unavailable`**: Service temporarily unavailable
- **`unauthenticated`**: User not logged in
- **`not-found`**: Document or collection doesn't exist
- **`already-exists`**: Document already exists
- **`resource-exhausted`**: Quota exceeded

### Custom Error Messages
- **`User not found`**: User profile missing from Firestore
- **`already_solved`**: Problem already completed
- **`invalid_format`**: Flag format incorrect
- **`incorrect_flag`**: Wrong flag submitted

## 🚀 Quick Fixes

### 1. Complete Reset
```bash
# Clear all browser data
1. Open browser settings
2. Clear browsing data
3. Select "All time"
4. Clear cookies, cache, and site data
5. Restart browser
6. Login again
```

### 2. Alternative Browser Test
```bash
# Test in different browser
1. Open Chrome/Firefox/Safari
2. Navigate to CTF platform
3. Login with same credentials
4. Try flag submission
```

### 3. Network Reset
```bash
# Reset network connection
1. Disconnect from WiFi
2. Wait 10 seconds
3. Reconnect to WiFi
4. Refresh page
5. Try submission again
```

## 📊 Monitoring & Logging

### Submission Logs
All flag submissions are logged to Firestore:
```typescript
// Submission log structure
{
  userId: string,
  problemId: string,
  submittedFlag: string,
  isCorrect: boolean,
  timestamp: Date,
  userAgent: string,
  ip: string
}
```

### Error Tracking
Errors are logged to browser console:
```javascript
// Check console for errors
console.error('Flag submission error:', error);
```

## 🔧 Developer Solutions

### 1. Update Firestore Rules
```javascript
// Ensure proper rules in firestore.rules
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /users/{userId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && request.auth.uid == userId;
    }
    
    match /submissions/{submissionId} {
      allow create: if request.auth != null 
        && request.auth.uid == request.resource.data.userId;
    }
  }
}
```

### 2. Deploy Rules
```bash
# Deploy updated rules
firebase deploy --only firestore:rules
```

### 3. Test Rules
```bash
# Test rules in Firebase console
# Go to Firestore > Rules > Rules playground
# Test different scenarios
```

## 📞 Support Checklist

Before contacting support, verify:
- [ ] Internet connection is stable
- [ ] User is properly logged in
- [ ] Flag format is correct (WOLF{...})
- [ ] Problem hasn't been solved already
- [ ] Browser cache has been cleared
- [ ] Different browser has been tested
- [ ] Error message has been noted
- [ ] Console errors have been checked

## 🎯 Prevention Tips

1. **Regular Logout/Login**: Refresh authentication tokens
2. **Stable Connection**: Use reliable internet connection
3. **Correct Format**: Always use WOLF{...} format
4. **Single Submission**: Don't spam submit button
5. **Browser Updates**: Keep browser updated
6. **Clear Cache**: Regular cache clearing

---

🚩 **Flag submission should now work properly with enhanced error handling and troubleshooting!**

If issues persist, check the browser console for specific error messages and follow the appropriate troubleshooting steps.
