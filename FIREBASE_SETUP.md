# 🔥 Firebase Setup for Wolf CTF Challenge

## Prerequisites
Before running the application, you need to configure Firebase for authentication and storage.

## Setup Steps

### 1. Create Firebase Project
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Click "Add project" and create a new project
3. Give your project a name (e.g., "wolf-ctf-community")
4. Continue through the setup process

### 2. Enable Authentication
1. In your Firebase project, go to "Authentication"
2. Click "Get started"
3. Go to "Sign-in method" tab
4. Enable "Email/Password" authentication

### 3. Enable Firestore Database
1. Go to "Firestore Database"
2. Click "Create database"
3. Choose "Start in test mode" for development
4. Select your preferred location

### 4. Enable Storage
1. Go to "Storage"
2. Click "Get started"
3. Accept the default security rules for now

### 5. Get Firebase Configuration
1. Go to "Project Settings" (gear icon)
2. Scroll down to "Your apps"
3. Click on "Web" icon (</>)
4. Register your app with a name
5. Copy the Firebase configuration object

### 6. Update Firebase Configuration
Open `src/lib/firebase.ts` and replace the placeholder config with your actual Firebase configuration:

```typescript
const firebaseConfig = {
  apiKey: "your-actual-api-key",
  authDomain: "your-project.firebaseapp.com",
  projectId: "your-actual-project-id",
  storageBucket: "your-project.appspot.com", 
  messagingSenderId: "your-sender-id",
  appId: "your-app-id"
};
```

### 7. Security Rules (Optional - for production)

**Firestore Rules:**
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
  }
}
```

**Storage Rules:**
```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    match /{allPaths=**} {
      allow read, write: if request.auth != null;
    }
  }
}
```

## 🚀 Running the Application

After completing the Firebase setup:

1. Install dependencies:
   ```bash
   npm install
   ```

2. Start the development server:
   ```bash
   npm run dev
   ```

3. Open http://localhost:8080 in your browser

## 🎯 CTF Features

- **5 Security Problems**: Each worth 50 points (250 total)
- **Real-time Scoring**: Progress tracked in Firestore
- **Secure Flags**: Stored securely in Firebase
- **Anti-Tampering**: Right-click and dev tools blocking
- **User Profiles**: Track individual progress and achievements

## 🛡️ Security Features

- Email/password authentication
- User session management  
- Flag validation system
- Anti-debugging measures
- Secure score tracking

Enjoy hacking! 🔥