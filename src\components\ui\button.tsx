import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap font-mono font-bold text-sm uppercase tracking-wider ring-offset-background transition-all duration-brutal focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground border-2 border-foreground shadow-brutal hover:translate-x-1 hover:translate-y-1 hover:shadow-none",
        destructive: "bg-destructive text-destructive-foreground border-2 border-foreground shadow-brutal hover:translate-x-1 hover:translate-y-1 hover:shadow-none",
        outline: "border-2 border-foreground bg-background hover:bg-foreground hover:text-background shadow-brutal hover:translate-x-1 hover:translate-y-1 hover:shadow-none",
        secondary: "bg-secondary text-secondary-foreground border-2 border-foreground shadow-brutal hover:translate-x-1 hover:translate-y-1 hover:shadow-none",
        accent: "bg-accent text-accent-foreground border-2 border-foreground shadow-brutal hover:translate-x-1 hover:translate-y-1 hover:shadow-none",
        ghost: "hover:bg-muted border-2 border-transparent hover:border-foreground",
        link: "text-foreground underline-offset-4 hover:underline border-none shadow-none",
      },
      size: {
        default: "h-12 px-6 py-3",
        sm: "h-10 px-4 py-2",
        lg: "h-14 px-8 py-4",
        icon: "h-12 w-12",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }
