rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users collection - allow authenticated users to read/write their own data
    match /users/{userId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && request.auth.uid == userId;
      allow write: if request.auth != null && isAdmin();
    }

    // CTF Submissions collection
    match /submissions/{submissionId} {
      allow read, write: if request.auth != null;
    }

    // Admin collection
    match /admin/{document=**} {
      allow read, write: if request.auth != null && isAdmin();
    }

    // Default rule for all other collections
    match /{document=**} {
      allow read, write: if request.auth != null;
    }
  }

  // Helper function for admin check
  function isAdmin() {
    return request.auth != null &&
           request.auth.token.email == '<EMAIL>';
  }
}

