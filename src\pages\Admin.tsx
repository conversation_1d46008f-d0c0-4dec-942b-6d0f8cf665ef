import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/components/AuthProvider';
import { useToast } from '@/hooks/use-toast';
import { useNavigate } from 'react-router-dom';
import { 
  collection, 
  query, 
  orderBy, 
  onSnapshot, 
  doc, 
  updateDoc, 
  deleteDoc,
  getDocs 
} from 'firebase/firestore';
import { db, logOut } from '@/lib/firebase';
import { Trash2, Edit, Save, X, Shield, Users, Trophy, AlertTriangle } from 'lucide-react';

interface UserData {
  id: string;
  email: string;
  displayName: string;
  fullName: string;
  score: number;
  solvedProblems: string[];
  createdAt: Date;
  lastSolved?: Date;
}

const Admin = () => {
  const { currentUser, loading } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();
  const [users, setUsers] = useState<UserData[]>([]);
  const [loadingUsers, setLoadingUsers] = useState(true);
  const [editingUser, setEditingUser] = useState<string | null>(null);
  const [editScore, setEditScore] = useState<number>(0);
  const [isAdmin, setIsAdmin] = useState(false);

  // Admin email check
  const adminEmails = ['<EMAIL>', 'tamilselvanadmin'];

  useEffect(() => {
    if (!loading && currentUser) {
      const userIsAdmin = adminEmails.includes(currentUser.email || '');
      setIsAdmin(userIsAdmin);
      
      if (!userIsAdmin) {
        toast({
          variant: "destructive",
          title: "ACCESS DENIED",
          description: "You don't have admin privileges",
        });
        navigate('/ctf');
        return;
      }
    }
  }, [currentUser, loading, navigate, toast]);

  useEffect(() => {
    if (!isAdmin) return;

    // Real-time users data
    const q = query(
      collection(db, 'users'),
      orderBy('score', 'desc'),
      orderBy('createdAt', 'asc')
    );

    const unsubscribe = onSnapshot(q, 
      (snapshot) => {
        const usersData: UserData[] = [];
        snapshot.forEach((doc) => {
          const data = doc.data();
          usersData.push({
            id: doc.id,
            email: data.email || '',
            displayName: data.displayName || 'Anonymous',
            fullName: data.fullName || 'Anonymous',
            score: data.score || 0,
            solvedProblems: data.solvedProblems || [],
            createdAt: data.createdAt?.toDate() || new Date(),
            lastSolved: data.lastSolved?.toDate()
          });
        });
        setUsers(usersData);
        setLoadingUsers(false);
      },
      (error) => {
        console.error('Users fetch error:', error);
        toast({
          variant: "destructive",
          title: "DATA ERROR",
          description: "Failed to load users data",
        });
        setLoadingUsers(false);
      }
    );

    return () => unsubscribe();
  }, [isAdmin, toast]);

  const handleEditScore = (userId: string, currentScore: number) => {
    setEditingUser(userId);
    setEditScore(currentScore);
  };

  const handleSaveScore = async (userId: string) => {
    try {
      const userRef = doc(db, 'users', userId);
      await updateDoc(userRef, {
        score: editScore
      });
      
      toast({
        title: "SCORE UPDATED",
        description: `Score updated to ${editScore} points`,
      });
      
      setEditingUser(null);
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "UPDATE FAILED",
        description: error.message || "Failed to update score",
      });
    }
  };

  const handleDeleteUser = async (userId: string, userName: string) => {
    if (!confirm(`Are you sure you want to delete user "${userName}"? This action cannot be undone.`)) {
      return;
    }

    try {
      const userRef = doc(db, 'users', userId);
      await deleteDoc(userRef);
      
      toast({
        title: "USER DELETED",
        description: `User "${userName}" has been deleted`,
      });
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "DELETE FAILED",
        description: error.message || "Failed to delete user",
      });
    }
  };

  const handleLogout = async () => {
    try {
      await logOut();
      navigate('/');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  if (loading || !isAdmin) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="terminal-cursor text-4xl mb-4">VERIFYING ADMIN ACCESS</div>
          <p className="text-muted-foreground font-mono">Checking permissions...</p>
        </div>
      </div>
    );
  }

  const totalUsers = users.length;
  const activeUsers = users.filter(user => user.score > 0).length;
  const totalScore = users.reduce((sum, user) => sum + user.score, 0);

  return (
    <div className="min-h-screen bg-background p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <Shield className="w-8 h-8 text-terminal-green" />
            <div>
              <h1 className="text-3xl font-bold">ADMIN DASHBOARD</h1>
              <p className="text-muted-foreground font-mono">Wolf CTF Challenge Management</p>
            </div>
          </div>
          
          <div className="flex items-center gap-4">
            <Button 
              variant="outline" 
              onClick={() => navigate('/ctf')}
              className="border-2"
            >
              🏠 CTF HOME
            </Button>
            <Button 
              variant="destructive" 
              onClick={handleLogout}
              className="border-2"
            >
              🚪 LOGOUT
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <Card className="p-6 border-2 border-foreground">
            <div className="flex items-center gap-4">
              <Users className="w-8 h-8 text-blue-500" />
              <div>
                <div className="text-2xl font-bold">{totalUsers}</div>
                <div className="text-sm text-muted-foreground">TOTAL USERS</div>
              </div>
            </div>
          </Card>
          
          <Card className="p-6 border-2 border-foreground">
            <div className="flex items-center gap-4">
              <Trophy className="w-8 h-8 text-terminal-green" />
              <div>
                <div className="text-2xl font-bold">{activeUsers}</div>
                <div className="text-sm text-muted-foreground">ACTIVE PLAYERS</div>
              </div>
            </div>
          </Card>
          
          <Card className="p-6 border-2 border-foreground">
            <div className="flex items-center gap-4">
              <AlertTriangle className="w-8 h-8 text-yellow-500" />
              <div>
                <div className="text-2xl font-bold">{totalScore}</div>
                <div className="text-sm text-muted-foreground">TOTAL POINTS</div>
              </div>
            </div>
          </Card>
        </div>

        {/* Users Management */}
        <Card className="p-6 border-2 border-foreground">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold">👥 USER MANAGEMENT</h2>
            <Badge className="bg-terminal-green text-black font-bold">
              ADMIN ONLY
            </Badge>
          </div>

          {loadingUsers ? (
            <div className="text-center py-8">
              <div className="terminal-cursor text-2xl mb-4">LOADING USERS</div>
              <p className="text-muted-foreground font-mono">Fetching user data...</p>
            </div>
          ) : users.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground font-mono">No users found</p>
            </div>
          ) : (
            <div className="space-y-4">
              {users.map((user, index) => (
                <div
                  key={user.id}
                  className="flex items-center justify-between p-4 border-2 border-foreground bg-background hover:bg-accent/5 transition-all duration-300"
                >
                  <div className="flex items-center gap-4 flex-1">
                    <div className="text-2xl font-bold text-muted-foreground">
                      #{index + 1}
                    </div>
                    <div className="flex-1">
                      <div className="font-bold text-lg">{user.displayName}</div>
                      <div className="text-sm text-muted-foreground font-mono">
                        {user.email}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {user.solvedProblems.length}/5 problems • Joined: {user.createdAt.toLocaleDateString()}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-4">
                    {/* Score Edit */}
                    <div className="flex items-center gap-2">
                      {editingUser === user.id ? (
                        <div className="flex items-center gap-2">
                          <Input
                            type="number"
                            value={editScore}
                            onChange={(e) => setEditScore(parseInt(e.target.value) || 0)}
                            className="w-20 h-8 text-center"
                            min="0"
                            max="1000"
                          />
                          <Button
                            size="sm"
                            onClick={() => handleSaveScore(user.id)}
                            className="h-8 w-8 p-0"
                          >
                            <Save className="w-4 h-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => setEditingUser(null)}
                            className="h-8 w-8 p-0"
                          >
                            <X className="w-4 h-4" />
                          </Button>
                        </div>
                      ) : (
                        <div className="flex items-center gap-2">
                          <div className="text-xl font-bold min-w-[60px] text-center">
                            {user.score}
                          </div>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleEditScore(user.id, user.score)}
                            className="h-8 w-8 p-0"
                          >
                            <Edit className="w-4 h-4" />
                          </Button>
                        </div>
                      )}
                    </div>
                    
                    {/* Delete Button */}
                    <Button
                      size="sm"
                      variant="destructive"
                      onClick={() => handleDeleteUser(user.id, user.displayName)}
                      className="h-8 w-8 p-0"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </Card>
      </div>
    </div>
  );
};

export default Admin;
