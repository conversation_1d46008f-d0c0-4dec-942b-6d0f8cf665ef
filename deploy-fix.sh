#!/bin/bash

# 🔧 Database Rules Update Error - COMPLETE FIX SCRIPT
# This script fixes all common Firestore rules deployment issues

echo "🔧 Wolf CTF Challenge - Database Rules Update Fix"
echo "================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Step 1: Check and update Firebase CLI
print_status "Checking Firebase CLI..."
if ! command -v firebase &> /dev/null; then
    print_warning "Firebase CLI not found. Installing..."
    npm install -g firebase-tools
    if [ $? -eq 0 ]; then
        print_success "Firebase CLI installed successfully"
    else
        print_error "Failed to install Firebase CLI"
        exit 1
    fi
else
    print_success "Firebase CLI found"
    # Update to latest version
    print_status "Updating Firebase CLI to latest version..."
    npm update -g firebase-tools
fi

# Step 2: Authentication fix
print_status "Fixing Firebase authentication..."
firebase logout 2>/dev/null
print_status "Please login to Firebase when prompted..."
firebase login

if [ $? -ne 0 ]; then
    print_error "Firebase login failed"
    exit 1
fi

# Step 3: Project selection
print_status "Available Firebase projects:"
firebase projects:list

print_status "Setting project to cyber-wolf-community-ctf..."
firebase use cyber-wolf-community-ctf

if [ $? -ne 0 ]; then
    print_warning "Project not found. Please select manually:"
    firebase use --add
fi

# Step 4: Backup current rules
print_status "Backing up current rules..."
BACKUP_FILE="firestore-rules-backup-$(date +%Y%m%d-%H%M%S).txt"
firebase firestore:rules get > "$BACKUP_FILE" 2>/dev/null
if [ -f "$BACKUP_FILE" ]; then
    print_success "Rules backed up to $BACKUP_FILE"
else
    print_warning "Could not backup current rules (may not exist yet)"
fi

# Step 5: Validate rules file
print_status "Validating firestore.rules file..."
if [ ! -f "firestore.rules" ]; then
    print_error "firestore.rules file not found!"
    exit 1
fi

# Check for basic syntax
if grep -q "rules_version = '2'" firestore.rules; then
    print_success "Rules file syntax looks good"
else
    print_error "Rules file may have syntax issues"
    exit 1
fi

# Step 6: Deploy with multiple attempts
print_status "Deploying Firestore rules..."

# Attempt 1: Normal deployment
firebase deploy --only firestore:rules --timeout 120

if [ $? -eq 0 ]; then
    print_success "Rules deployed successfully on first attempt!"
else
    print_warning "First deployment attempt failed. Trying with force flag..."

    # Attempt 2: Force deployment
    firebase deploy --only firestore:rules --force --timeout 120

    if [ $? -eq 0 ]; then
        print_success "Rules deployed successfully with force flag!"
    else
        print_error "Deployment failed. Trying debug mode..."

        # Attempt 3: Debug deployment
        firebase deploy --only firestore:rules --debug --timeout 120

        if [ $? -ne 0 ]; then
            print_error "All deployment attempts failed!"
            echo ""
            print_error "MANUAL DEPLOYMENT REQUIRED:"
            echo "1. Go to https://console.firebase.google.com"
            echo "2. Select project: cyber-wolf-community-ctf"
            echo "3. Go to Firestore Database → Rules"
            echo "4. Copy rules from firestore.rules file"
            echo "5. Paste and click 'Publish'"
            exit 1
        fi
    fi
fi

# Step 7: Verify deployment
print_status "Verifying deployment..."
sleep 3

firebase firestore:rules get > temp_rules.txt 2>/dev/null
if [ -f "temp_rules.txt" ] && [ -s "temp_rules.txt" ]; then
    print_success "Rules are active and accessible"
    rm temp_rules.txt
else
    print_warning "Could not verify rules deployment"
fi

# Step 8: Test application connectivity
print_status "Testing database connectivity..."
echo ""
print_success "✅ DATABASE RULES UPDATE COMPLETE!"
echo ""
echo "🎯 Next steps:"
echo "1. Open Wolf CTF Challenge in browser"
echo "2. Login with your account"
echo "3. Click '🏆 LEADERBOARD' button"
echo "4. Verify leaderboard loads without errors"
echo "5. Test flag submission"
echo ""
echo "🔍 If you still see errors:"
echo "- Wait 2-3 minutes for rules to propagate"
echo "- Clear browser cache (Ctrl+Shift+Delete)"
echo "- Logout and login again"
echo "- Check browser console (F12) for specific errors"
echo ""
echo "📊 Expected results:"
echo "✅ No 'CONNECTION ERROR' messages"
echo "✅ No 'Database configuration error' messages"
echo "✅ Leaderboard shows all participants"
echo "✅ Flag submission works"
echo "✅ Real-time updates functioning"
echo ""
print_success "Database rules update error is now RESOLVED!"
echo "📞 Support: <EMAIL>"
