@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:ital,wght@0,100..800;1,100..800&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Brutalist CTF Design System - High contrast monochrome with yellow accent */

@layer base {
  :root {
    /* Brutalist Color Palette */
    --background: 0 0% 100%;
    --foreground: 0 0% 0%;
    
    --card: 0 0% 100%;
    --card-foreground: 0 0% 0%;
    
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 0%;
    
    /* High contrast primary */
    --primary: 0 0% 0%;
    --primary-foreground: 0 0% 100%;
    
    /* Light gray secondary */
    --secondary: 0 0% 90%;
    --secondary-foreground: 0 0% 0%;
    
    /* Muted grays */
    --muted: 0 0% 95%;
    --muted-foreground: 0 0% 45%;
    
    /* Yellow accent for CTF theme */
    --accent: 51 100% 50%;
    --accent-foreground: 0 0% 0%;
    
    --destructive: 0 84% 50%;
    --destructive-foreground: 0 0% 100%;
    
    /* High contrast borders */
    --border: 0 0% 0%;
    --input: 0 0% 0%;
    --ring: 0 0% 0%;
    
    /* Sharp edges for brutalist design */
    --radius: 0rem;

    /* CTF Terminal Colors */
    --terminal-green: 120 100% 40%;
    --terminal-red: 0 100% 50%;
    --warning: 45 100% 50%;
    
    /* Shadows for depth */
    --shadow-brutal: 4px 4px 0px hsl(0 0% 0%);
    --shadow-brutal-lg: 8px 8px 0px hsl(0 0% 0%);
    
    /* Animation timing */
    --transition-brutal: all 0.1s ease;

    /* Sidebar variables */
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  
  .dark {
    /* Dark mode: Invert the brutalist theme */
    --background: 0 0% 0%;
    --foreground: 0 0% 100%;
    
    --card: 0 0% 5%;
    --card-foreground: 0 0% 100%;
    
    --popover: 0 0% 5%;
    --popover-foreground: 0 0% 100%;
    
    --primary: 0 0% 100%;
    --primary-foreground: 0 0% 0%;
    
    --secondary: 0 0% 10%;
    --secondary-foreground: 0 0% 100%;
    
    --muted: 0 0% 8%;
    --muted-foreground: 0 0% 60%;
    
    --accent: 51 100% 50%;
    --accent-foreground: 0 0% 0%;
    
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;
    
    --border: 0 0% 100%;
    --input: 0 0% 100%;
    --ring: 0 0% 100%;
    
    --shadow-brutal: 4px 4px 0px hsl(0 0% 100%);
    --shadow-brutal-lg: 8px 8px 0px hsl(0 0% 100%);
  }

  * {
    @apply border-border;
    font-family: 'JetBrains Mono', monospace;
  }

  body {
    @apply bg-background text-foreground font-mono;
    user-select: none; /* Prevent text selection for security */
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    -webkit-touch-callout: none; /* Disable iOS callout */
    -webkit-tap-highlight-color: transparent; /* Remove tap highlight */
  }
  
  /* Brutalist typography */
  h1, h2, h3, h4, h5, h6 {
    font-weight: 800;
    letter-spacing: -0.025em;
    text-transform: uppercase;
  }
  
  /* Terminal-style animations */
  .terminal-cursor::after {
    content: '█';
    animation: blink 1s infinite;
  }
  
  @keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
  }
  
  /* Brutalist button styles */
  .btn-brutal {
    border: 3px solid hsl(var(--foreground));
    box-shadow: var(--shadow-brutal);
    transition: var(--transition-brutal);
  }
  
  .btn-brutal:hover {
    transform: translate(2px, 2px);
    box-shadow: 2px 2px 0px hsl(var(--foreground));
  }
  
  .btn-brutal:active {
    transform: translate(4px, 4px);
    box-shadow: none;
  }

  /* Enhanced anti-debugging and security measures */
  .no-select {
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: transparent;
  }

  /* Security: Hide scrollbars to prevent inspection */
  ::-webkit-scrollbar {
    width: 8px;
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background: hsl(var(--muted-foreground));
    border-radius: 0;
  }

  /* Security: Prevent image dragging */
  img {
    -webkit-user-drag: none;
    -khtml-user-drag: none;
    -moz-user-drag: none;
    -o-user-drag: none;
    user-drag: none;
    pointer-events: none;
  }

  /* Security: Prevent text highlighting on important elements */
  .ctf-flag, .ctf-problem, .security-sensitive {
    user-select: none !important;
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    -webkit-touch-callout: none !important;
  }

  /* Security: Anti-screenshot blur effect */
  .blur-on-focus-loss {
    transition: filter 0.3s ease;
  }

  .blur-on-focus-loss.blurred {
    filter: blur(10px);
  }
}