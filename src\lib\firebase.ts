import { initializeApp } from 'firebase/app';
import { getAuth, GoogleAuthProvider, signInWithEmailAndPassword, createUserWithEmailAndPassword, signOut } from 'firebase/auth';
import { getFirestore, doc, setDoc, getDoc, updateDoc, increment, serverTimestamp, runTransaction } from 'firebase/firestore';
import { getStorage, ref, uploadBytes, getDownloadURL } from 'firebase/storage';

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyDN0E7ombncbYj-_iLhcEYxUGHb1FWo-6E",
  authDomain: "cyber-wolf-community-ctf.firebaseapp.com",
  projectId: "cyber-wolf-community-ctf",
  storageBucket: "cyber-wolf-community-ctf.firebasestorage.app",
  messagingSenderId: "370646269039",
  appId: "1:370646269039:web:e0566975c79d91d669219a",
  measurementId: "G-NH35HDL4N6"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase services
export const auth = getAuth(app);
export const db = getFirestore(app);
export const storage = getStorage(app);

// Auth providers
export const googleProvider = new GoogleAuthProvider();

// Auth functions
export const signInUser = (email: string, password: string) => 
  signInWithEmailAndPassword(auth, email, password);

export const signUpUser = (email: string, password: string) => 
  createUserWithEmailAndPassword(auth, email, password);

export const logOut = async () => {
  try {
    await signOut(auth);
    // Clear any cached data
    localStorage.clear();
    sessionStorage.clear();
    return true;
  } catch (error) {
    console.error('Logout error:', error);
    throw error;
  }
};

// Enhanced user profile management system
export const createUserProfile = async (userId: string, userData: any) => {
  try {
    const userRef = doc(db, 'users', userId);
    const profileData = {
      uid: userId,
      email: userData.email || '',
      displayName: userData.displayName || userData.email?.split('@')[0] || 'Anonymous',
      fullName: userData.fullName || userData.displayName || userData.email?.split('@')[0] || 'Anonymous',
      score: 0,
      solvedProblems: [],
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      lastLogin: serverTimestamp(),
      profileComplete: true,
      isActive: true
    };

    await setDoc(userRef, profileData);
    console.log('User profile created successfully:', userId);
    return profileData;
  } catch (error) {
    console.error('Error creating user profile:', error);
    throw error;
  }
};

export const getUserProfile = async (userId: string) => {
  try {
    const userRef = doc(db, 'users', userId);
    const userSnap = await getDoc(userRef);

    if (userSnap.exists()) {
      return userSnap.data();
    } else {
      console.log('User profile not found, will create new profile');
      return null;
    }
  } catch (error) {
    console.error('Error getting user profile:', error);
    throw error;
  }
};

// Get or create user profile (ensures profile always exists)
export const getOrCreateUserProfile = async (userId: string, userEmail: string, displayName?: string) => {
  try {
    // First try to get existing profile
    let profile = await getUserProfile(userId);

    if (!profile) {
      // Create new profile if doesn't exist
      console.log('Creating new user profile for:', userEmail);
      profile = await createUserProfile(userId, {
        email: userEmail,
        displayName: displayName || userEmail.split('@')[0],
        fullName: displayName || userEmail.split('@')[0]
      });
    } else {
      // Update last login time
      const userRef = doc(db, 'users', userId);
      await updateDoc(userRef, {
        lastLogin: serverTimestamp(),
        updatedAt: serverTimestamp()
      });
    }

    return profile;
  } catch (error) {
    console.error('Error in getOrCreateUserProfile:', error);
    throw error;
  }
};

// Enhanced flag submission with better auth handling and error recovery
export const submitFlag = async (userId: string, problemId: string, submittedFlag: string, correctFlag: string, points: number) => {
  // Validate user authentication first
  if (!userId) {
    return { success: false, reason: 'not_authenticated', message: 'Please login to submit flags.' };
  }

  // Input validation
  if (!submittedFlag || typeof submittedFlag !== 'string') {
    return { success: false, reason: 'invalid_input', message: 'Please enter a flag' };
  }

  const cleanFlag = submittedFlag.trim();

  // Validate flag format
  if (!cleanFlag.startsWith('WOLF{') || !cleanFlag.endsWith('}')) {
    return { success: false, reason: 'invalid_format', message: 'Flag must be in format: WOLF{...}' };
  }

  // Check minimum length
  if (cleanFlag.length < 8) {
    return { success: false, reason: 'invalid_length', message: 'Flag is too short' };
  }

  try {
    // First, verify user exists and get current state
    const userRef = doc(db, 'users', userId);
    const userSnap = await getDoc(userRef);

    if (!userSnap.exists()) {
      // Try to create user profile if it doesn't exist
      console.log('User profile not found, this might be a new user');
      return { success: false, reason: 'profile_missing', message: 'User profile not found. Please refresh the page and try again.' };
    }

    const userData = userSnap.data();
    const currentSolvedProblems = userData.solvedProblems || [];

    // Check if already solved BEFORE transaction
    if (currentSolvedProblems.includes(problemId)) {
      return { success: false, reason: 'already_solved', message: 'You have already solved this problem!' };
    }

    // Check flag correctness BEFORE transaction
    if (cleanFlag !== correctFlag.trim()) {
      // Log failed attempt asynchronously
      setTimeout(() => {
        logSubmissionAttempt(userId, problemId, '[INCORRECT]', false).catch(console.warn);
      }, 0);
      return { success: false, reason: 'incorrect_flag', message: 'Incorrect flag. Keep trying!' };
    }

    // Now perform the atomic update
    const result = await runTransaction(db, async (transaction) => {
      // Re-read user data in transaction to ensure consistency
      const userDoc = await transaction.get(userRef);

      if (!userDoc.exists()) {
        throw new Error('User profile disappeared during transaction');
      }

      const transactionUserData = userDoc.data();
      const transactionSolvedProblems = transactionUserData.solvedProblems || [];
      const transactionScore = transactionUserData.score || 0;

      // Double-check if already solved (race condition protection)
      if (transactionSolvedProblems.includes(problemId)) {
        return { success: false, reason: 'already_solved', message: 'You have already solved this problem!' };
      }

      // Update user data atomically
      const newSolvedProblems = [...transactionSolvedProblems, problemId];
      const newScore = transactionScore + points;

      transaction.update(userRef, {
        score: newScore,
        solvedProblems: newSolvedProblems,
        lastSolved: serverTimestamp(),
        updatedAt: serverTimestamp()
      });

      return {
        success: true,
        newScore: newScore,
        totalSolved: newSolvedProblems.length,
        message: `Flag captured! Earned ${points} points!`
      };
    });

    // Log successful submission asynchronously
    if (result.success) {
      setTimeout(() => {
        logSubmissionAttempt(userId, problemId, cleanFlag, true).catch(console.warn);
      }, 0);
    }

    return result;

  } catch (error: any) {
    console.error('Flag submission error:', error);

    // Handle specific Firebase errors with better messages
    if (error.code === 'permission-denied') {
      return { success: false, reason: 'permission_denied', message: 'Authentication expired. Please logout and login again.' };
    } else if (error.code === 'unavailable') {
      return { success: false, reason: 'unavailable', message: 'Service temporarily unavailable. Please try again in a moment.' };
    } else if (error.code === 'unauthenticated') {
      return { success: false, reason: 'unauthenticated', message: 'Session expired. Please login again.' };
    } else if (error.message.includes('User profile')) {
      return { success: false, reason: 'profile_error', message: 'Profile error. Please refresh the page and try again.' };
    } else {
      return { success: false, reason: 'system_error', message: `System error: ${error.message || 'Unknown error'}` };
    }
  }
};

// Simple fallback flag submission (if enhanced version fails)
export const submitFlagSimple = async (userId: string, problemId: string, submittedFlag: string, correctFlag: string, points: number) => {
  try {
    const cleanFlag = submittedFlag.trim();

    // Basic validation
    if (!cleanFlag.startsWith('WOLF{') || !cleanFlag.endsWith('}')) {
      return { success: false, reason: 'invalid_format', message: 'Flag must be in format: WOLF{...}' };
    }

    if (cleanFlag !== correctFlag.trim()) {
      return { success: false, reason: 'incorrect_flag', message: 'Incorrect flag. Keep trying!' };
    }

    // Use the legacy updateUserScore function
    const success = await updateUserScore(userId, points, problemId);

    if (success) {
      // Get updated user data to return new score
      const userRef = doc(db, 'users', userId);
      const userSnap = await getDoc(userRef);
      const newScore = userSnap.exists() ? userSnap.data().score : null;

      return {
        success: true,
        newScore: newScore,
        message: `Flag captured! Earned ${points} points!`
      };
    } else {
      return { success: false, reason: 'already_solved', message: 'You have already solved this problem!' };
    }
  } catch (error: any) {
    console.error('Simple flag submission error:', error);
    return { success: false, reason: 'system_error', message: 'System error. Please try again.' };
  }
};

// Legacy function for backward compatibility
export const updateUserScore = async (userId: string, points: number, problemId: string) => {
  const userRef = doc(db, 'users', userId);
  const userSnap = await getDoc(userRef);

  if (userSnap.exists()) {
    const userData = userSnap.data();
    if (!userData.solvedProblems.includes(problemId)) {
      await updateDoc(userRef, {
        score: increment(points),
        solvedProblems: [...userData.solvedProblems, problemId]
      });
      return true;
    }
  }
  return false;
};

// Log submission attempts for security monitoring
const logSubmissionAttempt = async (userId: string, problemId: string, submittedFlag: string, isCorrect: boolean) => {
  try {
    const submissionRef = doc(db, 'submissions', `${userId}_${problemId}_${Date.now()}`);
    await setDoc(submissionRef, {
      userId,
      problemId,
      submittedFlag: isCorrect ? submittedFlag : '[REDACTED]', // Don't log incorrect flags
      isCorrect,
      timestamp: serverTimestamp(),
      userAgent: navigator.userAgent,
      ip: 'client-side' // Would need server-side for real IP
    });
  } catch (error) {
    console.error('Failed to log submission:', error);
  }
};

// Enhanced CTF Problems data with separated URLs
export const ctfProblems = [
  {
    id: 'prob1',
    title: 'BUFFER OVERFLOW',
    description: 'Find the buffer overflow vulnerability in this C code snippet.',
    challengeUrl: 'https://techhack-25.web.app/code_file/e3b98a4da31a127d4bde6e43033f66ba274cab0eb7eb1c70ec41402bf6273dd8.html',
    points: 50,
    difficulty: 'EASY',
    flag: 'WOLF{buff3r_0v3rfl0w_b4s1cs}',
    hint: 'Look for unsafe string functions'
  },
  {
    id: 'prob2',
    title: 'SQL INJECTION',
    description: 'Exploit the SQL injection vulnerability to extract data.',
    challengeUrl: 'https://techhack-25.web.app/sql-pro/aaa9402664f1a41f40ebbc52c9993eb66aeb366602958fdfaa283b71e64db123.html',
    points: 50,
    difficulty: 'EASY',
    flag: 'WOLF{sql_1nj3ct10n_m4st3r}',
    hint: 'Try using UNION SELECT statements'
  },
  {
    id: 'prob3',
    title: 'XSS PAYLOAD',
    description: 'Craft an XSS payload that bypasses the filtering.',
    challengeUrl: 'https://techhack-25.web.app/capfile/56af4bde70a47ae7d0f1ebb30e45ed336165d5c9ec00ba9a92311e33a4256d74.html',
    points: 50,
    difficulty: 'MEDIUM',
    flag: 'WOLF{xss_byp4ss_3xp3rt}',
    hint: 'HTML encoding might help'
  },
  {
    id: 'prob4',
    title: 'CRYPTOGRAPHY',
    description: 'Decrypt the ROT13 encoded message to find the flag.',
    challengeUrl: 'https://techhack-25.web.app/CRYPTOGRAPHY/043a718774c572bd8a25adbeb1bfcd5c0256ae11cecf9f9c3f925d0e52beaf89.html',
    points: 50,
    difficulty: 'EASY',
    flag: 'WOLF{r0t13_d3crypt3d_fl4g}',
    hint: 'ROT13 is a simple Caesar cipher'
  },
  {
    id: 'prob5',
    title: 'PRIVILEGE ESCALATION',
    description: 'Find the privilege escalation vector in this Linux system.',
    challengeUrl: 'https://cyberwolf.wuaze.com/',
    points: 50,
    difficulty: 'HARD',
    flag: 'WOLF{pr1v_3sc4l4t10n_h4ck3r}',
    hint: 'Check for SUID binaries and sudo permissions'
  }
];