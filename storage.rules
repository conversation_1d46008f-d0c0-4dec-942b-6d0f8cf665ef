rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // User profile images - users can only upload/read their own images
    match /profile_images/{userId}/{allPaths=**} {
      allow read: if request.auth != null;
      allow write: if request.auth != null 
        && request.auth.uid == userId
        && isValidImageFile()
        && resource.size < 5 * 1024 * 1024; // 5MB limit
    }
    
    // CTF problem attachments - read-only for authenticated users
    match /ctf_problems/{problemId}/{allPaths=**} {
      allow read: if request.auth != null;
      allow write: if false; // Only admins should upload problem files
    }
    
    // User submissions/writeups - users can upload their own files
    match /submissions/{userId}/{allPaths=**} {
      allow read: if request.auth != null && request.auth.uid == userId;
      allow write: if request.auth != null 
        && request.auth.uid == userId
        && isValidSubmissionFile()
        && resource.size < 10 * 1024 * 1024; // 10MB limit
    }
    
    // Admin uploads - only for admin users
    match /admin/{allPaths=**} {
      allow read, write: if request.auth != null && isAdmin();
    }
    
    // Public assets - read-only for all authenticated users
    match /public/{allPaths=**} {
      allow read: if request.auth != null;
      allow write: if false; // Only server-side uploads
    }
  }
  
  // Helper functions
  function isValidImageFile() {
    return request.resource.contentType.matches('image/.*')
      && request.resource.contentType in [
        'image/jpeg', 
        'image/png', 
        'image/gif', 
        'image/webp'
      ];
  }
  
  function isValidSubmissionFile() {
    return request.resource.contentType in [
      'text/plain',
      'text/markdown',
      'application/pdf',
      'image/jpeg',
      'image/png',
      'application/zip'
    ];
  }
  
  function isAdmin() {
    // Add admin email addresses here
    return request.auth.token.email in [
      '<EMAIL>',
      'tamilselvanadmin'
    ];
  }
}
