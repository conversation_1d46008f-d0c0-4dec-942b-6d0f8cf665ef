import { createContext, useContext, useEffect, useState } from 'react';
import { User, onAuthStateChanged } from 'firebase/auth';
import { auth, getOrCreateUserProfile } from '@/lib/firebase';

interface AuthContextType {
  currentUser: User | null;
  loading: boolean;
  profileInitialized: boolean;
}

const AuthContext = createContext<AuthContextType>({
  currentUser: null,
  loading: true,
  profileInitialized: false
});

export const useAuth = () => useContext(AuthContext);

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [profileInitialized, setProfileInitialized] = useState(false);

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      setCurrentUser(user);

      if (user && user.email) {
        try {
          // Automatically create or update user profile
          console.log('Initializing user profile for:', user.email);
          await getOrCreateUserProfile(user.uid, user.email, user.displayName || undefined);
          setProfileInitialized(true);
          console.log('User profile initialized successfully');
        } catch (error) {
          console.error('Failed to initialize user profile:', error);
          setProfileInitialized(false);
        }
      } else {
        setProfileInitialized(false);
      }

      setLoading(false);
    });

    return unsubscribe;
  }, []);

  const value = {
    currentUser,
    loading,
    profileInitialized
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};