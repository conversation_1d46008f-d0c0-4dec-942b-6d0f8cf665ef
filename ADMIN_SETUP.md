# 🛡️ Admin Dashboard Setup Guide

## Overview
The Admin Dashboard provides comprehensive management capabilities for the Wolf CTF Challenge platform, allowing authorized administrators to view, edit, and manage all participant data.

## Admin Features

### 🔐 Access Control
- **Admin Email Verification**: Only users with authorized email addresses can access the admin dashboard
- **Automatic Redirection**: Non-admin users are automatically redirected to the CTF page
- **Secure Authentication**: Admin status is verified on both client and server side

### 📊 Dashboard Features

#### User Management
- **View All Participants**: Complete list of all registered users
- **Real-time Updates**: Live data synchronization with Firebase
- **User Statistics**: Total users, active players, and total points
- **Detailed User Info**: Email, display name, join date, and progress

#### Score Management
- **Edit Scores**: Click the edit button next to any user's score
- **Real-time Updates**: Changes are immediately reflected across the platform
- **Validation**: Score inputs are validated (0-1000 points)
- **Audit Trail**: All changes are logged in Firebase

#### User Administration
- **Delete Users**: Remove users from the platform (with confirmation)
- **User Details**: View solved problems, join date, and activity
- **Progress Tracking**: See individual user progress (X/5 problems solved)

## Admin Configuration

### Current Admin Users
The following email addresses have admin privileges:
- `<EMAIL>`
- `tamilselvanadmin`

### Adding New Admins
To add new admin users, update the admin email list in multiple files:

1. **Frontend Admin Check** (`src/pages/Admin.tsx`):
```typescript
const adminEmails = ['<EMAIL>', 'tamilselvanadmin', '<EMAIL>'];
```

2. **CTF Page Admin Button** (`src/pages/CTF.tsx`):
```typescript
const adminEmails = ['<EMAIL>', 'tamilselvanadmin', '<EMAIL>'];
```

3. **Firestore Security Rules** (`firestore.rules`):
```javascript
function isAdmin() {
  return request.auth.token.email in [
    '<EMAIL>',
    'tamilselvanadmin',
    '<EMAIL>'
  ];
}
```

4. **Storage Security Rules** (`storage.rules`):
```javascript
function isAdmin() {
  return request.auth.token.email in [
    '<EMAIL>',
    'tamilselvanadmin',
    '<EMAIL>'
  ];
}
```

## Accessing the Admin Dashboard

### For Admin Users
1. **Login**: Sign in with an authorized admin email
2. **Navigate**: Go to the CTF page (`/ctf`)
3. **Admin Button**: Click the "🛡️ ADMIN" button in the header
4. **Direct Access**: Navigate directly to `/admin`

### Admin Dashboard URL
```
https://your-domain.com/admin
```

## Security Features

### Firebase Rules
- **Admin-Only Access**: Only admin users can read/write all user data
- **Data Validation**: All updates are validated for proper structure
- **Audit Logging**: All admin actions are logged in Firebase

### Frontend Security
- **Email Verification**: Admin status checked against authorized email list
- **Route Protection**: Non-admin users cannot access admin routes
- **Real-time Validation**: Admin status verified on every page load

## Dashboard Statistics

### Overview Cards
- **Total Users**: Count of all registered participants
- **Active Players**: Users with score > 0
- **Total Points**: Sum of all user scores across the platform

### User Management Table
- **Ranking**: Users sorted by score (highest first)
- **User Details**: Name, email, join date, progress
- **Score Editing**: Inline score editing with save/cancel options
- **User Deletion**: Remove users with confirmation dialog

## Best Practices

### Score Management
- **Fair Play**: Only adjust scores for legitimate reasons
- **Documentation**: Keep records of score adjustments
- **Validation**: Ensure scores are within reasonable ranges (0-1000)

### User Management
- **Confirmation**: Always confirm before deleting users
- **Backup**: Consider exporting user data before major changes
- **Communication**: Inform users of any account changes

### Security
- **Regular Review**: Periodically review admin access list
- **Secure Credentials**: Use strong passwords for admin accounts
- **Activity Monitoring**: Monitor admin dashboard usage

## Troubleshooting

### Common Issues

#### "Access Denied" Error
- **Cause**: User email not in admin list
- **Solution**: Add email to all admin configuration files

#### Score Updates Not Saving
- **Cause**: Firebase rules or network issues
- **Solution**: Check Firebase console and network connectivity

#### Users Not Loading
- **Cause**: Firestore permissions or query issues
- **Solution**: Verify Firestore rules and database structure

### Debug Steps
1. Check browser console for errors
2. Verify Firebase rules are deployed
3. Confirm admin email is correctly configured
4. Test with Firebase emulator for development

## Development

### Local Testing
```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Access admin dashboard
http://localhost:5173/admin
```

### Firebase Emulator
```bash
# Start Firebase emulators
firebase emulators:start

# Test admin functionality locally
```

## Production Deployment

### Pre-deployment Checklist
- [ ] Update admin email addresses
- [ ] Deploy Firebase rules
- [ ] Test admin functionality
- [ ] Verify security measures
- [ ] Document any changes

### Deployment Commands
```bash
# Deploy Firebase rules
firebase deploy --only firestore:rules,storage

# Deploy application
npm run build
# Deploy to your hosting platform
```

---

🔥 **Your admin dashboard is now ready for managing the Wolf CTF Community!**

For support or questions, contact the development team.
