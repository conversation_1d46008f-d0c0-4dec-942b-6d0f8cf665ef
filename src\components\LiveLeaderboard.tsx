import { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { db } from '@/lib/firebase';
import { collection, query, orderBy, limit, onSnapshot, getDocs } from 'firebase/firestore';
import { useToast } from '@/hooks/use-toast';

interface LeaderboardEntry {
  id: string;
  displayName: string;
  fullName: string;
  email: string;
  score: number;
  solvedProblems: string[];
  lastSolved?: Date;
}

export const LiveLeaderboard = () => {
  const [leaderboard, setLeaderboard] = useState<LeaderboardEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [isRetrying, setIsRetrying] = useState(false);
  const { toast } = useToast();

  // Fallback function for Firestore internal errors
  const setupFallbackLeaderboard = async () => {
    try {
      console.log('Setting up fallback leaderboard...');

      // Use a simpler query without complex ordering
      const usersRef = collection(db, 'users');
      const snapshot = await getDocs(usersRef);

      const leaderboardData: LeaderboardEntry[] = [];
      snapshot.forEach((doc) => {
        const data = doc.data();
        leaderboardData.push({
          id: doc.id,
          displayName: data.displayName || data.fullName || 'Anonymous',
          fullName: data.fullName || 'Anonymous',
          email: data.email || '',
          score: data.score || 0,
          solvedProblems: data.solvedProblems || [],
          lastSolved: data.lastSolved?.toDate()
        });
      });

      // Sort manually in JavaScript
      leaderboardData.sort((a, b) => {
        if (b.score !== a.score) {
          return b.score - a.score;
        }
        return 0;
      });

      setLeaderboard(leaderboardData);
      setLoading(false);
      setError(null);
      console.log(`Fallback leaderboard loaded: ${leaderboardData.length} participants`);

    } catch (error) {
      console.error('Fallback leaderboard failed:', error);
      setError("Unable to load leaderboard. Please refresh the page.");
      setLoading(false);
    }
  };

  // Retry function for failed connections
  const retryConnection = async () => {
    if (retryCount >= 3) {
      setError("Maximum retry attempts reached. Trying fallback method...");
      setIsRetrying(false);
      setupFallbackLeaderboard();
      return;
    }

    setIsRetrying(true);
    setRetryCount(prev => prev + 1);

    // Wait before retrying (exponential backoff)
    const delay = Math.pow(2, retryCount) * 1000; // 1s, 2s, 4s
    await new Promise(resolve => setTimeout(resolve, delay));

    // Reset states and try again
    setError(null);
    setLoading(true);
    setIsRetrying(false);
  };

  useEffect(() => {
    let unsubscribe: (() => void) | null = null;
    let fallbackTimeout: NodeJS.Timeout | null = null;

    const setupLeaderboard = async () => {
      try {
        // Clear any existing timeout
        if (fallbackTimeout) {
          clearTimeout(fallbackTimeout);
        }

        // Simplified query to avoid Firestore internal errors
        const q = query(
          collection(db, 'users'),
          orderBy('score', 'desc'),
          limit(50) // Show more participants
        );

        // Set a fallback timeout in case of internal errors
        fallbackTimeout = setTimeout(() => {
          console.warn('Firestore query taking too long, trying fallback...');
          setupFallbackLeaderboard();
        }, 10000); // 10 second timeout

        unsubscribe = onSnapshot(q,
          (snapshot) => {
            try {
              const leaderboardData: LeaderboardEntry[] = [];
              snapshot.forEach((doc) => {
                const data = doc.data();
                // Show ALL users regardless of score
                leaderboardData.push({
                  id: doc.id,
                  displayName: data.displayName || data.fullName || 'Anonymous',
                  fullName: data.fullName || 'Anonymous',
                  email: data.email || '',
                  score: data.score || 0,
                  solvedProblems: data.solvedProblems || [],
                  lastSolved: data.lastSolved?.toDate()
                });
              });

              // Sort by score descending, then by creation time if available
              leaderboardData.sort((a, b) => {
                if (b.score !== a.score) {
                  return b.score - a.score;
                }
                // If scores are equal, sort by who solved problems first
                if (a.lastSolved && b.lastSolved) {
                  return a.lastSolved.getTime() - b.lastSolved.getTime();
                }
                return 0;
              });

              setLeaderboard(leaderboardData);
              setLoading(false);
              setError(null);
              setRetryCount(0); // Reset retry count on success

              console.log(`Leaderboard loaded successfully: ${leaderboardData.length} participants`);
            } catch (err) {
              console.error('Error processing leaderboard data:', err);
              setError("Error processing leaderboard data");
              setLoading(false);
            }
          },
          (error) => {
            console.error('Leaderboard connection error:', error);

            // Clear fallback timeout since we got an error
            if (fallbackTimeout) {
              clearTimeout(fallbackTimeout);
            }

            let errorMessage = "Failed to load leaderboard";
            let shouldRetry = true;
            let useFallback = false;

            // Handle specific Firebase errors with better messages
            if (error.code === 'permission-denied') {
              errorMessage = "Permission denied. Please logout and login again.";
              shouldRetry = false;
            } else if (error.code === 'unavailable') {
              errorMessage = "Firebase service temporarily unavailable. Retrying...";
            } else if (error.code === 'failed-precondition') {
              errorMessage = "Database rules need to be updated. Deploying fix...";
              shouldRetry = true;
            } else if (error.code === 'unauthenticated') {
              errorMessage = "Authentication expired. Please login again.";
              shouldRetry = false;
            } else if (error.message?.includes('network')) {
              errorMessage = "Network connection error. Check your internet connection.";
            } else if (error.message?.includes('quota')) {
              errorMessage = "Database quota exceeded. Please try again later.";
              shouldRetry = true;
            } else if (error.message?.includes('INTERNAL ASSERTION FAILED') ||
                       error.message?.includes('Unexpected state')) {
              // Handle Firestore internal errors
              errorMessage = "Database internal error. Switching to fallback mode...";
              useFallback = true;
              shouldRetry = false;
            }

            setError(errorMessage);
            setLoading(false);

            // Show toast notification
            toast({
              variant: "destructive",
              title: "🌐 CONNECTION ERROR",
              description: errorMessage,
            });

            // Use fallback for internal errors, retry for others
            if (useFallback) {
              setTimeout(() => {
                setupFallbackLeaderboard();
              }, 1000);
            } else if (shouldRetry && retryCount < 3) {
              setTimeout(() => {
                retryConnection();
              }, 2000);
            }
          }
        );
      } catch (err) {
        console.error('Error setting up leaderboard:', err);
        setError("Failed to initialize leaderboard");
        setLoading(false);
      }
    };

    setupLeaderboard();

    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }, [toast, retryCount]);

  const getTrophyIcon = (index: number) => {
    switch (index) {
      case 0: return '🏆'; // Gold Trophy for 1st place
      case 1: return '🥈'; // Silver Medal for 2nd place
      case 2: return '🥉'; // Bronze Medal for 3rd place
      default: return null; // No trophy for other places
    }
  };



  const getProgressColor = (score: number) => {
    if (score >= 250) return 'bg-terminal-green';
    if (score >= 200) return 'bg-accent';
    if (score >= 150) return 'bg-warning';
    if (score >= 100) return 'bg-destructive';
    return 'bg-muted';
  };

  if (loading) {
    return (
      <Card className="p-6">
        <div className="text-center">
          <div className="terminal-cursor text-2xl mb-4">
            {isRetrying ? 'RETRYING' : 'LOADING'}
          </div>
          <p className="text-muted-foreground font-mono">
            {isRetrying
              ? `Retry attempt ${retryCount}/3... Please wait.`
              : 'Fetching live scores...'
            }
          </p>
          {isRetrying && (
            <div className="mt-4">
              <div className="w-full h-2 bg-muted border border-foreground relative overflow-hidden">
                <div className="h-full bg-terminal-green animate-pulse"></div>
              </div>
            </div>
          )}
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="p-6">
        <div className="text-center">
          <div className="text-4xl mb-4">⚠️</div>
          <h3 className="text-xl font-bold mb-2">CONNECTION ERROR</h3>
          <p className="text-muted-foreground font-mono mb-4">{error}</p>
          <div className="flex flex-col sm:flex-row gap-2 justify-center">
            <button
              onClick={retryConnection}
              disabled={isRetrying || retryCount >= 3}
              className="px-4 py-2 bg-accent text-accent-foreground border-2 border-foreground font-bold hover:bg-accent/80 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isRetrying ? 'RETRYING...' : retryCount >= 3 ? 'MAX RETRIES REACHED' : 'RETRY CONNECTION'}
            </button>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-destructive text-destructive-foreground border-2 border-foreground font-bold hover:bg-destructive/80"
            >
              REFRESH PAGE
            </button>
          </div>
          {retryCount > 0 && (
            <p className="text-xs text-muted-foreground mt-2">
              Retry attempts: {retryCount}/3
            </p>
          )}
        </div>
      </Card>
    );
  }

  return (
    <Card className="p-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 gap-2">
        <h2 className="text-xl sm:text-2xl font-bold">🏆 LIVE LEADERBOARD</h2>
        <div className="flex items-center gap-4">
          {/* Connection Status */}
          <div className="flex items-center gap-2">
            <div className={`w-2 h-2 rounded-full ${
              error ? 'bg-destructive' : 'bg-terminal-green animate-pulse'
            }`}></div>
            <span className="text-xs sm:text-sm font-mono text-muted-foreground">
              {error ? 'OFFLINE' : 'LIVE'}
            </span>
          </div>

          {/* Participant Count */}
          <div className="flex items-center gap-2">
            <span className="text-xs sm:text-sm font-mono text-muted-foreground">
              {leaderboard.length} PARTICIPANTS
            </span>
          </div>

          {/* Manual Refresh Button */}
          <button
            onClick={() => window.location.reload()}
            className="text-xs sm:text-sm font-mono text-muted-foreground hover:text-foreground transition-colors"
            title="Refresh leaderboard"
          >
            🔄 REFRESH
          </button>
        </div>
      </div>

      {leaderboard.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-muted-foreground font-mono">No participants yet. Join the challenge!</p>
        </div>
      ) : (
        <div className="space-y-6">
          {/* All Participants with Top 3 Highlighted */}
          <div>
            <h3 className="text-lg sm:text-xl font-bold mb-6 text-center">🏆 ALL PARTICIPANTS LEADERBOARD 🏆</h3>

            {/* Single List with All Participants */}
            <div className="space-y-3">
              {leaderboard.map((entry, index) => {
                const isTopThree = index < 3;
                const position = index + 1;

                return (
                  <div
                    key={entry.id}
                    className={`flex items-center justify-between p-4 sm:p-6 border-2 transition-all duration-300 ${
                      isTopThree
                        ? index === 0
                          ? 'bg-gradient-to-r from-yellow-500/20 to-yellow-600/10 border-yellow-500 shadow-lg'
                          : index === 1
                          ? 'bg-gradient-to-r from-gray-400/20 to-gray-500/10 border-gray-400 shadow-lg'
                          : 'bg-gradient-to-r from-orange-600/20 to-orange-700/10 border-orange-600 shadow-lg'
                        : entry.score === 0
                        ? 'border-muted bg-muted/10 hover:bg-muted/20'
                        : 'border-foreground bg-background/50 hover:bg-accent/5'
                    }`}
                  >
                    {/* Left Side: Rank, Trophy, and Player Info */}
                    <div className="flex items-center gap-4 flex-1 min-w-0">
                      {/* Rank/Trophy */}
                      <div className="flex items-center justify-center min-w-[60px]">
                        {isTopThree ? (
                          <div className="text-center">
                            <div className={`text-3xl sm:text-4xl ${
                              index === 0 ? '🏆' : index === 1 ? '🥈' : '🥉'
                            }`}>
                              {getTrophyIcon(index)}
                            </div>
                            <div className={`text-xs font-bold ${
                              index === 0 ? 'text-yellow-600' :
                              index === 1 ? 'text-gray-600' : 'text-orange-600'
                            }`}>
                              #{position}
                            </div>
                          </div>
                        ) : (
                          <div className="text-center">
                            <div className={`text-xl font-bold ${
                              entry.score === 0 ? 'text-muted-foreground' : 'text-foreground'
                            }`}>
                              #{position}
                            </div>
                          </div>
                        )}
                      </div>

                      {/* Player Info */}
                      <div className="min-w-0 flex-1">
                        <div className={`font-bold text-lg sm:text-xl truncate ${
                          isTopThree ? 'text-foreground' :
                          entry.score === 0 ? 'text-muted-foreground' : 'text-foreground'
                        }`} title={entry.displayName}>
                          {entry.displayName}
                          {entry.score === 0 && (
                            <span className="ml-2 text-xs bg-muted px-2 py-1 rounded">NEW</span>
                          )}
                          {isTopThree && (
                            <span className={`ml-2 text-xs px-2 py-1 rounded font-bold ${
                              index === 0 ? 'bg-yellow-500 text-black' :
                              index === 1 ? 'bg-gray-400 text-black' : 'bg-orange-600 text-white'
                            }`}>
                              {index === 0 ? 'CHAMPION' : index === 1 ? 'RUNNER-UP' : 'THIRD PLACE'}
                            </span>
                          )}
                        </div>
                        <div className="text-sm text-muted-foreground font-mono">
                          {entry.solvedProblems.length}/5 problems solved
                          {entry.score === 0 && entry.solvedProblems.length === 0 && (
                            <span className="ml-2 text-warning">• Not started</span>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Right Side: Score and Progress */}
                    <div className="flex items-center gap-4 flex-shrink-0">
                      <div className="text-right">
                        <div className={`text-2xl sm:text-3xl font-bold ${
                          isTopThree ? 'text-foreground' :
                          entry.score === 0 ? 'text-muted-foreground' : 'text-foreground'
                        }`}>
                          {entry.score}
                        </div>
                        <div className="text-xs text-muted-foreground">POINTS</div>
                      </div>

                      {/* Progress Bar */}
                      <div className="w-16 sm:w-20 h-4 sm:h-5 bg-muted border border-foreground relative">
                        <div
                          className={`h-full ${getProgressColor(entry.score)} transition-all duration-500`}
                          style={{ width: `${Math.min((entry.score / 250) * 100, 100)}%` }}
                        />
                        {entry.score === 0 && (
                          <div className="absolute inset-0 flex items-center justify-center">
                            <span className="text-xs text-muted-foreground">0%</span>
                          </div>
                        )}
                      </div>

                      {/* Badges */}
                      {entry.score >= 250 ? (
                        <Badge className="bg-terminal-green text-black font-bold">
                          ELITE
                        </Badge>
                      ) : entry.score === 0 ? (
                        <Badge className="bg-muted text-muted-foreground font-bold">
                          STARTER
                        </Badge>
                      ) : isTopThree ? (
                        <Badge className={`font-bold ${
                          index === 0 ? 'bg-yellow-500 text-black' :
                          index === 1 ? 'bg-gray-400 text-black' : 'bg-orange-600 text-white'
                        }`}>
                          TOP 3
                        </Badge>
                      ) : null}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      )}

      <div className="mt-6 sm:mt-8 p-4 sm:p-6 bg-muted border-2 border-foreground">
        <h3 className="text-base sm:text-lg font-bold mb-3 sm:mb-4 text-center">📈 COMPETITION STATISTICS</h3>
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 lg:gap-6 text-center">
          <div className="p-2 sm:p-3 bg-background border border-foreground">
            <div className="text-lg sm:text-2xl font-bold text-terminal-green">{leaderboard.length}</div>
            <div className="text-xs sm:text-sm text-muted-foreground font-mono">TOTAL PARTICIPANTS</div>
          </div>
          <div className="p-2 sm:p-3 bg-background border border-foreground">
            <div className="text-lg sm:text-2xl font-bold text-blue-500">
              {leaderboard.filter(e => e.score > 0).length}
            </div>
            <div className="text-xs sm:text-sm text-muted-foreground font-mono">ACTIVE PLAYERS</div>
          </div>
          <div className="p-2 sm:p-3 bg-background border border-foreground">
            <div className="text-lg sm:text-2xl font-bold text-yellow-500">
              {leaderboard.filter(e => e.score >= 250).length}
            </div>
            <div className="text-xs sm:text-sm text-muted-foreground font-mono">ELITE HACKERS</div>
          </div>
          <div className="p-2 sm:p-3 bg-background border border-foreground">
            <div className="text-lg sm:text-2xl font-bold text-purple-500">
              {leaderboard.length > 0 ? Math.max(...leaderboard.map(e => e.score)) : 0}
            </div>
            <div className="text-xs sm:text-sm text-muted-foreground font-mono">HIGHEST SCORE</div>
          </div>
        </div>

        {/* Competition Progress */}
        <div className="mt-4 sm:mt-6 p-3 sm:p-4 bg-background border border-foreground">
          <div className="space-y-3">
            {/* Elite Progress */}
            <div>
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-2 gap-1 sm:gap-0">
                <span className="text-xs sm:text-sm font-mono">ELITE COMPLETION</span>
                <span className="text-xs sm:text-sm font-mono">
                  {leaderboard.filter(e => e.score >= 250).length} / {leaderboard.length} ELITE
                </span>
              </div>
              <div className="w-full h-3 sm:h-4 bg-muted border border-foreground relative">
                <div
                  className="h-full bg-terminal-green transition-all duration-500"
                  style={{
                    width: leaderboard.length > 0
                      ? `${(leaderboard.filter(e => e.score >= 250).length / leaderboard.length) * 100}%`
                      : '0%'
                  }}
                />
              </div>
            </div>

            {/* Active Participation */}
            <div>
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-2 gap-1 sm:gap-0">
                <span className="text-xs sm:text-sm font-mono">ACTIVE PARTICIPATION</span>
                <span className="text-xs sm:text-sm font-mono">
                  {leaderboard.filter(e => e.score > 0).length} / {leaderboard.length} STARTED
                </span>
              </div>
              <div className="w-full h-3 sm:h-4 bg-muted border border-foreground relative">
                <div
                  className="h-full bg-blue-500 transition-all duration-500"
                  style={{
                    width: leaderboard.length > 0
                      ? `${(leaderboard.filter(e => e.score > 0).length / leaderboard.length) * 100}%`
                      : '0%'
                  }}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
};