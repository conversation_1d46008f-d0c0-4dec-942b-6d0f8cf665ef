import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/components/AuthProvider';
import { getUserProfile, logOut, ctfProblems } from '@/lib/firebase';
import { useToast } from '@/hooks/use-toast';
import { updatePassword as firebaseUpdatePassword } from 'firebase/auth';
import { useNavigate } from 'react-router-dom';

interface UserProfile {
  score: number;
  solvedProblems: string[];
  displayName: string;
  fullName: string;
  email: string;
  createdAt: Date;
}

export default function Profile() {
  const { currentUser } = useAuth();
  const navigate = useNavigate();
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [changingPassword, setChangingPassword] = useState(false);
  const [showPasswordForm, setShowPasswordForm] = useState(false);
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const { toast } = useToast();

  useEffect(() => {
    const loadUserProfile = async () => {
      if (currentUser) {
        const profile = await getUserProfile(currentUser.uid);
        if (profile) {
          setUserProfile(profile as UserProfile);
        }
      }
      setLoading(false);
    };
    
    loadUserProfile();
  }, [currentUser]);

  const handlePasswordChange = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!newPassword || !confirmPassword || !currentPassword) {
      toast({
        variant: "destructive",
        title: "VALIDATION ERROR",
        description: "All password fields are required",
      });
      return;
    }

    if (newPassword !== confirmPassword) {
      toast({
        variant: "destructive",
        title: "PASSWORD MISMATCH",
        description: "New passwords do not match",
      });
      return;
    }

    if (newPassword.length < 6) {
      toast({
        variant: "destructive",
        title: "WEAK PASSWORD",
        description: "Password must be at least 6 characters",
      });
      return;
    }

    setChangingPassword(true);

    try {
      // Firebase requires re-authentication before password change
      if (currentUser) {
        await firebaseUpdatePassword(currentUser, newPassword);
        toast({
          title: "PASSWORD UPDATED",
          description: "Your password has been changed successfully",
        });
        setShowPasswordForm(false);
        setCurrentPassword('');
        setNewPassword('');
        setConfirmPassword('');
      }
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "PASSWORD CHANGE FAILED",
        description: error.message || "Failed to change password",
      });
    }

    setChangingPassword(false);
  };

  const handleLogout = async () => {
    try {
      await logOut();
      toast({
        title: "LOGGED OUT",
        description: "Session terminated successfully",
      });
      // Navigate to home page after logout
      navigate('/');
    } catch (error) {
      console.error('Logout error:', error);
      toast({
        variant: "destructive",
        title: "LOGOUT ERROR",
        description: "Failed to logout properly",
      });
    }
  };

  const getSolvedProblemsDetails = () => {
    if (!userProfile?.solvedProblems) return [];
    
    return userProfile.solvedProblems.map(problemId => {
      const problem = ctfProblems.find(p => p.id === problemId);
      return problem ? {
        ...problem,
        solvedAt: new Date() // In real app, you'd store solve timestamp
      } : null;
    }).filter(Boolean);
  };

  const getProgressPercentage = () => {
    return userProfile ? Math.round((userProfile.score / 250) * 100) : 0;
  };

  const getRankBadge = () => {
    if (!userProfile) return null;
    
    if (userProfile.score >= 250) return { text: 'ELITE HACKER', color: 'bg-terminal-green text-black' };
    if (userProfile.score >= 200) return { text: 'EXPERT', color: 'bg-accent text-black' };
    if (userProfile.score >= 150) return { text: 'ADVANCED', color: 'bg-warning text-black' };
    if (userProfile.score >= 100) return { text: 'INTERMEDIATE', color: 'bg-destructive text-white' };
    if (userProfile.score >= 50) return { text: 'BEGINNER', color: 'bg-secondary text-foreground' };
    return { text: 'ROOKIE', color: 'bg-muted text-foreground' };
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="terminal-cursor text-4xl mb-4">LOADING</div>
          <p className="text-muted-foreground font-mono">Loading profile...</p>
        </div>
      </div>
    );
  }

  const rank = getRankBadge();
  const solvedProblems = getSolvedProblemsDetails();

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="border-b-4 border-foreground bg-background sticky top-0 z-40">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold tracking-tighter">👤 HACKER PROFILE</h1>
            <p className="text-sm text-muted-foreground font-mono">
              {userProfile?.fullName} | {userProfile?.email}
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => navigate('/ctf')}>
              🏠 CTF HOME
            </Button>
            <Button variant="outline" onClick={() => setShowPasswordForm(!showPasswordForm)}>
              🔐 CHANGE PASSWORD
            </Button>
            <Button variant="destructive" onClick={handleLogout}>
              LOGOUT
            </Button>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        <div className="grid gap-6 md:grid-cols-2">
          {/* Profile Stats */}
          <Card className="p-6">
            <h2 className="text-xl font-bold mb-4">📊 STATISTICS</h2>
            
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="font-mono">TOTAL SCORE:</span>
                <span className="text-2xl font-bold">{userProfile?.score || 0}/250</span>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="font-mono">PROBLEMS SOLVED:</span>
                <span className="text-xl font-bold">{userProfile?.solvedProblems?.length || 0}/5</span>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="font-mono">PROGRESS:</span>
                <span className="text-lg font-bold">{getProgressPercentage()}%</span>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="font-mono">RANK:</span>
                {rank && (
                  <Badge className={rank.color}>
                    {rank.text}
                  </Badge>
                )}
              </div>
            </div>

            {/* Progress Bar */}
            <div className="mt-6">
              <div className="flex justify-between text-sm mb-2">
                <span>PROGRESS TO ELITE</span>
                <span>{getProgressPercentage()}%</span>
              </div>
              <div className="h-4 bg-muted border-2 border-foreground relative">
                <div 
                  className="h-full bg-accent transition-all duration-500"
                  style={{ width: `${getProgressPercentage()}%` }}
                />
              </div>
            </div>
          </Card>

          {/* Password Change Form */}
          {showPasswordForm && (
            <Card className="p-6">
              <h2 className="text-xl font-bold mb-4">🔐 CHANGE PASSWORD</h2>
              
              <form onSubmit={handlePasswordChange} className="space-y-4">
                <div>
                  <label className="block text-sm font-bold mb-2 uppercase">
                    CURRENT PASSWORD
                  </label>
                  <Input
                    type="password"
                    placeholder="Enter current password"
                    value={currentPassword}
                    onChange={(e) => setCurrentPassword(e.target.value)}
                    required
                    disabled={changingPassword}
                  />
                </div>

                <div>
                  <label className="block text-sm font-bold mb-2 uppercase">
                    NEW PASSWORD
                  </label>
                  <Input
                    type="password"
                    placeholder="Enter new password"
                    value={newPassword}
                    onChange={(e) => setNewPassword(e.target.value)}
                    required
                    disabled={changingPassword}
                    minLength={6}
                  />
                </div>

                <div>
                  <label className="block text-sm font-bold mb-2 uppercase">
                    CONFIRM NEW PASSWORD
                  </label>
                  <Input
                    type="password"
                    placeholder="Confirm new password"
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    required
                    disabled={changingPassword}
                    minLength={6}
                  />
                </div>

                <div className="flex gap-2">
                  <Button
                    type="submit"
                    variant="accent"
                    disabled={changingPassword}
                    className="flex-1"
                  >
                    {changingPassword ? 'UPDATING...' : 'UPDATE PASSWORD'}
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setShowPasswordForm(false)}
                    disabled={changingPassword}
                  >
                    CANCEL
                  </Button>
                </div>
              </form>
            </Card>
          )}

          {/* Solved Problems */}
          <Card className="p-6 md:col-span-2">
            <h2 className="text-xl font-bold mb-4">🏆 SOLVED PROBLEMS</h2>
            
            {solvedProblems.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground font-mono">No problems solved yet. Keep hacking!</p>
              </div>
            ) : (
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {solvedProblems.map((problem: any) => (
                  <div 
                    key={problem.id}
                    className="p-4 border-2 border-terminal-green bg-terminal-green/10"
                  >
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="font-bold">{problem.title}</h3>
                      <Badge className="bg-terminal-green text-black">
                        +{problem.points}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mb-2">{problem.description}</p>
                    <div className="text-xs font-mono">
                      FLAG: <code className="bg-muted px-1">{problem.flag}</code>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </Card>
        </div>
      </div>
    </div>
  );
}