import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card } from '@/components/ui/card';
import { signInUser, signUpUser, createUserProfile } from '@/lib/firebase';
import { useToast } from '@/hooks/use-toast';

interface AuthCardProps {
  onAuthSuccess: () => void;
}

export const AuthCard = ({ onAuthSuccess }: AuthCardProps) => {
  const [isSignIn, setIsSignIn] = useState(true);
  const [fullName, setFullName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validation for sign up
    if (!isSignIn) {
      if (!fullName.trim()) {
        toast({
          variant: "destructive",
          title: "VALIDATION ERROR",
          description: "Full name is required",
        });
        return;
      }
      
      if (password !== confirmPassword) {
        toast({
          variant: "destructive",
          title: "PASSWORD MISMATCH",
          description: "Passwords do not match",
        });
        return;
      }
      
      if (password.length < 6) {
        toast({
          variant: "destructive",
          title: "WEAK PASSWORD",
          description: "Password must be at least 6 characters",
        });
        return;
      }
    }

    setLoading(true);

    try {
      if (isSignIn) {
        await signInUser(email, password);
        toast({
          title: "ACCESS GRANTED",
          description: "Welcome back to Wolf CTF Challenge",
        });
      } else {
        const userCredential = await signUpUser(email, password);
        await createUserProfile(userCredential.user.uid, {
          email: email,
          displayName: fullName.trim(),
          fullName: fullName.trim()
        });
        toast({
          title: "ACCOUNT CREATED",
          description: `Welcome ${fullName}! Registration successful.`,
        });
      }
      onAuthSuccess();
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "ACCESS DENIED",
        description: error.message || "Authentication failed",
      });
    }
    
    setLoading(false);
  };

  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4">
      {/* Terminal-style background pattern */}
      <div className="fixed inset-0 opacity-30" style={{backgroundImage: "url('data:image/svg+xml,%3Csvg width=\"60\" height=\"60\" viewBox=\"0 0 60 60\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cg fill=\"none\" fill-rule=\"evenodd\"%3E%3Cg fill=\"%********\" fill-opacity=\"0.03\"%3E%3Cpath d=\"M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z\"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')"}}></div>
      
      <div className="relative w-full max-w-md">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold mb-2 tracking-tighter">
            WOLF CTF CHALLENGE
          </h1>
          <p className="text-muted-foreground font-mono">
            Wolf Authentication System
          </p>
        </div>

        {/* Auth Card */}
        <Card className="p-6 border-4 border-foreground shadow-brutal-lg bg-background">
          {/* Tab Buttons */}
          <div className="flex mb-6">
            <Button
              type="button"
              variant={isSignIn ? "default" : "outline"}
              className="flex-1 rounded-none"
              onClick={() => setIsSignIn(true)}
            >
              SIGN IN
            </Button>
            <Button
              type="button"
              variant={!isSignIn ? "default" : "outline"}
              className="flex-1 rounded-none border-l-0"
              onClick={() => setIsSignIn(false)}
            >
              SIGN UP
            </Button>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit} className="space-y-4">
            {!isSignIn && (
              <div>
                <label className="block text-sm font-bold mb-2 uppercase">
                  👤 FULL NAME
                </label>
                <Input
                  type="text"
                  placeholder="Enter your full name"
                  value={fullName}
                  onChange={(e) => setFullName(e.target.value)}
                  required={!isSignIn}
                  disabled={loading}
                />
              </div>
            )}

            <div>
              <label className="block text-sm font-bold mb-2 uppercase">
                📧 EMAIL ADDRESS
              </label>
              <Input
                type="email"
                placeholder="Enter your email address"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                disabled={loading}
              />
            </div>

            <div>
              <label className="block text-sm font-bold mb-2 uppercase">
                🔒 PASSWORD
              </label>
              <Input
                type="password"
                placeholder="Enter your password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
                disabled={loading}
                minLength={6}
              />
            </div>

            {!isSignIn && (
              <div>
                <label className="block text-sm font-bold mb-2 uppercase">
                  🔐 CONFIRM PASSWORD
                </label>
                <Input
                  type="password"
                  placeholder="Confirm your password"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  required={!isSignIn}
                  disabled={loading}
                  minLength={6}
                />
              </div>
            )}

            <Button
              type="submit"
              variant="accent"
              className="w-full mt-6 text-lg"
              disabled={loading}
            >
              {loading ? "PROCESSING..." : `🔐 SIGN ${isSignIn ? "IN" : "UP"}`}
            </Button>
          </form>
        </Card>

        {/* Footer */}
        <div className="text-center mt-6">
          <p className="text-xs text-muted-foreground font-mono">
            © 2025 Cyber Wolf | All rights reserved.
          </p>
        </div>
      </div>
    </div>
  );
};